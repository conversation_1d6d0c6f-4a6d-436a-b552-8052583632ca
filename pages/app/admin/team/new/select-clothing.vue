<template>
  <OnboardingTeamWrapper :current-group="2" :current-step="2">
    <template #left>
      <ButtonWhite size="sm" @click="goToPreviousStep">
        <IconChevron class="size-4 rotate-180 mr-2" />
        <span>Back</span>
      </ButtonWhite>
    </template>
    <template #actions>
      <ButtonWhite size="sm" @click="goToNextStep">
        <span>Skip this step</span>
      </ButtonWhite>
      <ButtonPrimary size="sm" @click="confirm">
        <span>Continue</span>
        <IconChevron class="size-4 ml-3" />
      </ButtonPrimary>
    </template>
    <template #mobile-actions>
      <ButtonPrimary :is-disabled="numberOfSelectedClothingItems === 0" class="w-full" @click="confirm">
        <span v-if="numberOfSelectedClothingItems > 0">
          Continue with {{ numberOfSelectedClothingItems }} attires
        </span>
        <span v-else>
          Please select at least one
        </span>
        <IconArrowRight class="ml-2 size-4" />
      </ButtonPrimary>
      <div class="text-center mt-1">
        <button type="button" class="text-sm text-paragraph" @click="goToNextStep">
          or <span class="underline">skip this step</span>
        </button>
      </div>
    </template>
    <template #mobile-nav-actions>
      <ButtonWhite size="sm" @click="showFaqModal = true">
        <IconSmallQuestionMark class="size-4 mr-2" />
        <span>FAQs</span>
      </ButtonWhite>
    </template>
    <div class="w-full grid grid-cols-1 gap-8 md:grid-cols-2 md:gap-16 justify-start items-start pt-8 md:pb-16">
      <div>
        <div class="max-w-[520px] mx-auto md:mx-0 flex flex-col items-center justify-center text-center md:block md:text-left">
          <PostcheckoutStepper :step="4" :max-steps="7" size="small" active="bg-primary-500" />
          <h1 class="text-lg md:text-[25px] font-bold text-primary-500 mt-6">
            Select your team's wardrobe
          </h1>
          <p class="text-sm md:text-base text-slate-500 font-medium mt-1 sm:mt-3">
            Approve which clothing items your team members are allowed to wear in their headshots.
          </p>
        </div>
      </div>
      <div class="hidden md:grid grid-cols-1 gap-2 pt-2">
        <FaqSmallCollapsibleItem v-for="(item, index) in slicedFaqItems" :key="'faq-item-' + index" :item="item" />
        <FaqSmallCollapsibleItem v-if="faqItems.length > 3" :item="viewAllFaqsItem" />
      </div>
    </div>
    <div class="pb-12 pt-4 sm:pt-8 md:pt-0">
      <CommonDismissibleInfoBlock storage-key="clothing-picker-help-hidden">
        <div class="flex items-start gap-3">
          <IconMiniInformation class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div class="flex-1">
            <h3 class="text-sm font-semibold text-blue-900 mb-1">
              How team outfit selection works
            </h3>
            <div class="text-sm text-blue-800 space-y-1">
              <p>• Team members will choose outfits from your approved list</p>
              <p>• Each member picks their preferred outfit for <strong>each of their 8 backdrops</strong></p>
              <p>• Members can choose different outfits for different backdrops</p>
              <p>• More approved options = greater flexibility and personalization for your team</p>
            </div>
          </div>
        </div>
      </CommonDismissibleInfoBlock>
      <Card>
        <div class="flex items-center justify-between gap-2">
          <p class="flex-1 text-base font-medium tracking-tight text-primary-500">
            Team wardrobe
          </p>
          <div class="items-center justify-end gap-3 hidden sm:flex">
            <template v-if="tab === 'selected'">
              <ButtonWhite v-if="numberOfSelectedClothingItems === 0" size="sm" @click="addRecommended">
                Add recommended
              </ButtonWhite>
              <ButtonWhite v-else size="sm" @click="removeAll">
                Remove all
              </ButtonWhite>
            </template>
            <template v-else>
              <ButtonWhite size="sm" @click="addAll">
                Add all
              </ButtonWhite>
            </template>
            <ButtonPrimary size="sm" :is-disabled="numberOfSelectedClothingItems === 0" @click="confirm">
              <span v-if="numberOfSelectedClothingItems > 0">
                Continue with {{ numberOfSelectedClothingItems }} attires
              </span>
              <span v-else>
                Please select at least one
              </span>
              <IconArrowRight class="ml-2 size-4" />
            </ButtonPrimary>
          </div>
          <div class="flex sm:hidden">
            <ButtonWhite size="sm" @click="expandFilters">
              <IconFilter class="size-4 mr-2" />
              Filters
            </ButtonWhite>
          </div>
        </div>

        <hr class="mt-4 border-gray-200">

        <OnboardingTeamClothingPicker
          ref="clothingPicker"
          @count="numberOfSelectedClothingItems = $event"
          @next="goToNextStep"
          @tab="tab = $event"
        />
      </Card>
    </div>
    <FaqModal v-if="showFaqModal" :faqs="faqItems" @closeModal="showFaqModal = false" />
  </OnboardingTeamWrapper>
</template>

<script>
import OrganizationMixin from '../../../../../mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  layout: 'protected',
  data () {
    return {
      showFaqModal: false,
      numberOfSelectedClothingItems: 0,
      tab: 'selected',
      viewAllFaqsItem: {
        question: 'View all frequently asked questions',
        onClick: () => {
          this.showFaqModal = true
        }
      },
      faqItems: [
        {
          question: 'What are clothing selections for?',
          answer: 'Your team members will wear one of the selected clothing items for each backdrop you selected in the previous step.'
        },
        {
          question: 'Does everyone get the same?',
          answer: 'No, each team member can select their own clothing from the options you selected.'
        },
        {
          question: 'Can I let my team pick themselves?',
          answer: 'Yes, you can let your team pick their own clothing.'
        }
      ]
    }
  },
  computed: {
    clothing () {
      return this.$store.state.clothing
    },
    slicedFaqItems () {
      if (this.faqItems.length <= 3) {
        return this.faqItems
      }

      return this.faqItems.slice(0, 2)
    }
  },
  mounted () {
    if (!this.completionData.hasDetails) {
      return this.$router.push('/app/admin/team/new/intro')
    }

    if (this.clothing.length === 0) {
      this.$store.dispatch('getClothing')
    }
    this.$posthog.capture('$organization:onboarding:select-clothing')

    window.addEventListener('onboarding:beforeNavigate', this.handleBeforeNavigate)
  },
  beforeDestroy () {
    window.removeEventListener('onboarding:beforeNavigate', this.handleBeforeNavigate)
  },
  methods: {
    handleBeforeNavigate (event) {
      this.$refs.clothingPicker.confirm(true)
    },
    removeAll () {
      this.$refs.clothingPicker.removeAll()
    },
    confirm () {
      this.$refs.clothingPicker.confirm()
    },
    addRecommended () {
      this.$refs.clothingPicker.addRecommended()
    },
    addAll () {
      this.$refs.clothingPicker.addAll()
    },
    expandFilters () {
      this.$refs.clothingPicker.expandFilters()
    },
    goToNextStep () {
      this.$router.push('/app/admin/team/new/branding')
    },
    goToPreviousStep () {
      this.$router.push('/app/admin/team/new/select-style')
    }
  }
}
</script>
