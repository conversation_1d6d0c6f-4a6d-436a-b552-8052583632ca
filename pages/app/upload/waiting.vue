<template>
  <PostcheckoutWrapper :skip="true">
    <template #left>
      <nuxt-link v-if="hasInvites && !isTeamMember" to="/app/invites" class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700">
        <span class="underline-none mr-1 flex h-4 w-4 animate-pulse items-center justify-center rounded-full bg-green-500 text-center text-xs text-white">!</span>
        <p class="text-sm text-gray-500 underline">
          Invites
        </p>
      </nuxt-link>
      <nuxt-link v-if="isTeamLead" to="/app/admin" class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700">
        For teams
      </nuxt-link>
      <template v-if="role !== 'TeamMember'">
        <nuxt-link
          v-if="$store?.state?.user?.stripeId || $store?.state?.user?.paypalId"
          to="/profile/invoices"
          class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700"
        >
          Invoices
        </nuxt-link>

        <button
          type="button"
          class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700"
          @click="$store.commit('SET_MODAL', { name: 'support', value: true })"
        >
          Support
        </button>
      </template>
    </template>
    <template #right>
      <nuxt-link v-if="$store?.state?.user?.stripeId" to="/profile" class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700">
        Profile
      </nuxt-link>
      <button
        type="button"
        class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700"
        @click="logout"
      >
        Logout
      </button>
    </template>

    <main class="flex-1">
      <!-- <div class="flex min-h-screen p-4 bg-gray-50"> -->
      <div class=" flex-1">
        <div class="px-2 py-12 lg:flex lg:items-center lg:justify-center sm:px-6 lg:px-16 xl:px-24 xl:py-16">
          <div class="space-y-8 lg:max-w-xl">
            <div>
              <div v-if="hoursAgo >= 4" class="bg-orange-50 border border-orange-100 py-2 px-4 rounded-md mb-4">
                <span class="text-sm font-medium text-gray-500">
                  {{ waitingText }}
                </span>
                <ButtonPrimary v-if="hoursAgo >= 4" size="sm" class="mt-2" @click="openSupportModal">
                  {{ $t('Report a bug') }}
                </ButtonPrimary>
              </div>
              <span v-else class="text-sm font-medium text-gray-500">
                {{ waitingText }}
              </span>
              <div class="relative h-2 mt-2 bg-gray-200 rounded-full w-72 overflow-hidden">
                <div class="absolute inset-y-0 bg-green-500 h-2 rounded-full w-[20%] infinite-progress" />
              </div>

              <div class="mt-4">
                <p class="text-xl font-bold tracking-tight text-primary-500">
                  {{ $t('Hold tight! We\'re preparing your headshots') }}
                </p>
                <p class="mt-2 text-base font-medium text-gray-500">
                  {{ $t('Our AI is hard at work to get your headshots ready. You may close this screen and come back later.') }}
                </p>
              </div>
            </div>

            <hr class="border-gray-200">

            <ul class="space-y-2.5 text-sm font-medium text-gray-500">
              <li class="flex items-center gap-1.5">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="text-green-500 size-5 -mb-0.5"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span>
                  <span class="font-medium text-gray-700">{{ $store.state.stats.photos }}</span> {{ $t('AI headshots already created') }}
                </span>
              </li>

              <li class="flex items-center gap-1.5">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="text-green-500 size-5 -mb-0.5"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span>
                  <span class="font-medium text-gray-700">{{ happyCustomers }}</span> {{ $t('happy customers') }}
                </span>
              </li>

              <li class="flex items-center gap-1.5">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="text-green-500 size-5 -mb-0.5"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span>
                  {{ $t('Founded in Holland.') }} <span class="font-medium text-gray-700">{{ $t('We respect your privacy.') }}</span>
                </span>
              </li>
            </ul>

            <hr class="border-gray-200">

            <div>
              <p class="text-base font-medium text-gray-500">
                {{ $t('Need help with your order?') }}
                <button
                  type="button"
                  class="font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700"
                  @click="openSupportModal"
                >
                  {{ $t('Contact support.') }}
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- </div> -->
    </main>
  </PostcheckoutWrapper>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      interval: null,
      hoursAgo: 0
    }
  },
  computed: {
    happyCustomers () {
      return Math.round(this.$store.state.stats.users.replace(/,/g, '') / 1000) + ',000+'
    },
    waitingText () {
      if (this.hoursAgo < 2) {
        return this.$t('This process can take up to 2 hours')
      } else if (this.hoursAgo < 4) {
        return this.$t('A recent surge of orders is causing results to take a little longer than usual right now. We\'ve received your order and will email you as soon as your headshots are completed.')
      } else if (this.hoursAgo >= 6) {
        return this.$t('Uh oh. We\'ve detected a small error with your order. Please click below to instantly report this bug and we\'ll take care of it ASAP.')
      }

      return this.$t('This process can take up to 2 hours')
    },
    activeShoots () {
      return this.shoots.filter((shoot) => {
        if (shoot?.favoriteImagesCount === 0) { return false }
        if (shoot?.status !== 'active') { return false }
        return true
      })
    },
    pendingShoots () {
      return this.shoots.filter(shoot => shoot?.status !== 'active')
    },
    env () {
      return process.env.NODE_ENV
    },
    isTeamLead () {
      return this.$store.state?.user?.role === 'TeamLead' || null
    },
    hasInvites () {
      return this.$store.state?.user?.invites || null
    },
    showInviteWarning () {
      return this.$store.state?.showInviteWarning || null
    },
    role () {
      return this.$store.state?.user?.role || null
    },
    purchasedUpsell () {
      return this.$store.state?.user?.purchasedUpsell || false
    }
  },
  mounted () {
    this.checkStatus()
    this.interval = setInterval(() => {
      this.checkStatus()
    }, 60000)
    setTimeout(() => {
      this.checkStatus()
    }, 3000)
  },
  beforeDestroy () {
    clearInterval(this.interval)
  },
  methods: {
    checkStatus () {
      this.$axios.$get('/model/all')
        .then((response) => {
          if (response.data?.length > 0) {
            const atLeastOneIsWaiting = response.data.some(model =>
              ['pending', 'waiting', 'generatingHeadshots'].includes(model.status)
            )
            if (!atLeastOneIsWaiting) {
              clearInterval(this.interval)
              this.$router.push(this.localePath('/app'))
            } else {
              // Get the latest model with status pending, waiting, or generatingHeadshots
              const latestWaitingModel = response.data
                .filter(model => ['pending', 'waiting', 'generatingHeadshots'].includes(model.status))
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0]
              if (latestWaitingModel) {
                const hoursAgo = Math.floor((latestWaitingModel.now - new Date(latestWaitingModel.createdAt).getTime()) / (1000 * 60 * 60))
                this.hoursAgo = hoursAgo
              }
            }
          }
        })
    },
    openSupportModal () {
      this.$store.commit('SET_MODAL', { name: 'support', value: true })
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "This process can take up to 2 hours": "This process can take up to 2 hours",
      "Hold tight! We're preparing your headshots": "Hold tight! We're preparing your headshots",
      "Our AI is hard at work to get your headshots ready. You may close this screen and come back later.": "Our AI is hard at work to get your headshots ready. You may close this screen and come back later.",
      "AI headshots already created": "AI headshots already created",
      "Need help with your order?": "Need help with your order?",
      "Contact support.": "Contact support."
    },
    "es": {
      "This process can take up to 2 hours": "Este proceso puede tardar hasta 2 horas",
      "Hold tight! We're preparing your headshots": "¡Mantente firme! Estamos preparando tus fotos",
      "Our AI is hard at work to get your headshots ready. You may close this screen and come back later.": "Nuestra IA está trabajando duro para preparar tus fotos. Puedes cerrar esta pantalla y volver más tarde.",
      "AI headshots already created": "Fotos de IA ya creadas",
      "Need help with your order?": "¿Necesitas ayuda con tu pedido?",
      "Contact support.": "Contacta con soporte."
    },
    "de": {
      "This process can take up to 2 hours": "Dieser Vorgang kann bis zu 2 Stunden dauern",
      "Hold tight! We're preparing your headshots": "Hab Geduld! Wir erstellen deine Bewerbungsfotos",
      "Our AI is hard at work to get your headshots ready. You may close this screen and come back later.": "Unsere KI arbeitet hart daran, deine Bewerbungsfotos fertigzustellen. Du kannst diesen Bildschirm schließen und später wiederkommen.",
      "AI headshots already created": "KI-Bewerbungsfotos bereits erstellt",
      "Need help with your order?": "Brauchst du Hilfe bei deiner Bestellung?",
      "Contact support.": "Kontaktiere unseren Support."
    }
  }
</i18n>
