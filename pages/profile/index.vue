<template>
  <div>
    <HeaderApplication />
    <div class="w-full p-8">
      <div class="max-w-6xl mx-auto">
        <p class="text-sm text-gray-600 mb-6">
          {{ $t('You\'re logged in as') }} <span class="font-semibold text-primary-500">{{ $store.state.user?.email }}</span>
        </p>
        <div>
          <div class="space-y-4">
            <div class="grid grid-cols-1 grid-rows-3 md:grid-cols-3 md:grid-rows-1 gap-8">
              <Card class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    {{ $t('Change email') }}
                  </h3>
                </CardHeader>
                <ProfileChangeEmailForm />
              </Card>
              <Card class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    {{ $t('Change password') }}
                  </h3>
                </CardHeader>
                <ProfileChangePasswordForm />
              </Card>
              <Card class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    {{ $t('Change name') }}
                  </h3>
                </CardHeader>
                <ProfileChangeNameForm />
              </Card>
              <!-- <Card v-if="$store?.state?.user?.stripeId" class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    Update payment method
                  </h3>
                </CardHeader>
                <div class="py-4">
                  <ButtonPrimary size="sm" @click="createPortalSession">
                    Change here
                  </ButtonPrimary>
                </div>
              </Card> -->
              <Card class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    {{ $t('User ID') }}
                  </h3>
                </CardHeader>
                <div class="py-4">
                  <p class="text-sm text-gray-500 mb-3">
                    {{ $t('Share this ID with support if assistance is needed') }}
                  </p>
                  <div class="bg-gray-50 p-3 rounded-md border">
                    <code class="text-sm font-mono text-gray-800 select-all">{{ $store.state.user?.uid }}</code>
                  </div>
                </div>
              </Card>
              <Card v-if="!isTeamMember" class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    {{ $t('Delete my account') }}
                  </h3>
                </CardHeader>
                <div class="py-4">
                  <ButtonDelete @click="showDeleteAccountModal = true">
                    {{ $t('Delete my account') }}
                  </ButtonDelete>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Popup v-if="showDeleteAccountModal" size="lg" @closeModal="showDeleteAccountModal = false">
      <LoadingWrapper :is-loading="isLoading">
        <div class="space-y-4">
          <div class="space-y-2 flex flex-col">
            <h2 class="text-lg font-semibold">
              {{ $t('Are you sure you want to delete your account?') }}
            </h2>
            <p class="text-sm text-gray-500">
              {{ $t('This action cannot be undone. We will delete all your data, your photos and any other information you have stored with us.') }}
            </p>
          </div>
          <ul class="text-sm text-gray-500 font-medium list-disc pl-4 space-y-1">
            <li>{{ $t('We won\'t be able to refund any payments you have made.') }}</li>
            <li>{{ $t('There is no way to restore your account once it is deleted.') }}</li>
          </ul>
          <Input v-model="deleteAccountConfirmationEmail" :placeholder="$t('Type the email above')" :label="$t('Type {email} to delete your account', { email: $store.state.user?.email })" />
          <div class="flex justify-start gap-2">
            <ButtonWhite size="sm" @click="showDeleteAccountModal = false">
              {{ $t('Cancel') }}
            </ButtonWhite>
            <ButtonDelete size="sm" @click="deleteAccount">
              {{ $t('Delete my account') }}
            </ButtonDelete>
          </div>
        </div>
      </LoadingWrapper>
    </Popup>
  </div>
</template>

<script>

export default {
  layout: 'protected',
  data () {
    return {
      showDeleteAccountModal: false,
      deleteAccountConfirmationEmail: '',
      isLoading: false
      // navigation: this.$store.state.navigation.settingsNavigation
    }
  },
  head () {
    return {
      title: this.$t('Profile | HeadshotPro')
    }
  },
  methods: {
    async createPortalSession () {
      this.isLoading = true
      const response = await this.$axios.$post('/checkout/stripe/create-customer-portal-session')
      if (response && response.url) {
        window.open(response.url, '_blank')
      }
      this.isLoading = false
    },
    deleteAccount () {
      if (this.deleteAccountConfirmationEmail !== this.$store.state.user?.email) { return this.$toast.error(this.$t('The email you typed does not match your account email.')) }
      this.isLoading = true
      this.$axios.$post('/user/delete').then((response) => {
        if (response.success) {
          this.$toast.success(this.$t('Your account has been deleted.'))
          this.logout()
        } else {
          this.$toast.error(response.message)
        }
      }).catch((err) => {
        console.log(err)
        this.$toast.error(err.message)
      }).finally(() => {
        this.isLoading = false
      })
    }
  }

}
</script>

<style>

</style>

<i18n>
  {
    "en": {
      "You're logged in as": "You're logged in as",
      "Change email": "Change email",
      "Change password": "Change password",
      "Change name": "Change name",
      "User ID": "User ID",
      "Share this ID with support if assistance is needed": "Share this ID with support if assistance is needed",
      "Delete my account": "Delete my account",
      "Are you sure you want to delete your account?": "Are you sure you want to delete your account?",
      "This action cannot be undone. We will delete all your data, your photos and any other information you have stored with us.": "This action cannot be undone. We will delete all your data, your photos and any other information you have stored with us.",
      "We won't be able to refund any payments you have made.": "We won't be able to refund any payments you have made.",
      "There is no way to restore your account once it is deleted.": "There is no way to restore your account once it is deleted.",
      "Type the email above": "Type the email above",
      "Type {email} to delete your account": "Type {email} to delete your account",
      "Cancel": "Cancel",
      "Profile | HeadshotPro": "Profile | HeadshotPro",
      "The email you typed does not match your account email.": "The email you typed does not match your account email.",
      "Your account has been deleted.": "Your account has been deleted."
    },
    "es": {
      "You're logged in as": "Has iniciado sesión como",
      "Change email": "Cambiar correo electrónico",
      "Change password": "Cambiar contraseña",
      "Change name": "Cambiar nombre",
      "User ID": "ID de usuario",
      "Share this ID with support if assistance is needed": "Comparte este ID con soporte si necesitas ayuda",
      "Delete my account": "Eliminar mi cuenta",
      "Are you sure you want to delete your account?": "¿Estás seguro de que quieres eliminar tu cuenta?",
      "This action cannot be undone. We will delete all your data, your photos and any other information you have stored with us.": "Esta acción no se puede deshacer. Eliminaremos todos tus datos, tus fotos y cualquier otra información que hayas almacenado con nosotros.",
      "We won't be able to refund any payments you have made.": "No podremos reembolsar ningún pago que hayas realizado.",
      "There is no way to restore your account once it is deleted.": "No hay forma de restaurar tu cuenta una vez que sea eliminada.",
      "Type the email above": "Escribe el correo electrónico de arriba",
      "Type {email} to delete your account": "Escribe {email} para eliminar tu cuenta",
      "Cancel": "Cancelar",
      "Profile | HeadshotPro": "Perfil | HeadshotPro",
      "The email you typed does not match your account email.": "El correo electrónico que escribiste no coincide con el correo de tu cuenta.",
      "Your account has been deleted.": "Tu cuenta ha sido eliminada."
    },
    "de": {
      "You're logged in as": "Du bist angemeldet als",
      "Change email": "E-Mail ändern",
      "Change password": "Passwort ändern",
      "Change name": "Name ändern",
      "User ID": "Benutzer-ID",
      "Share this ID with support if assistance is needed": "Teile diese ID mit dem Support, falls Hilfe benötigt wird",
      "Delete my account": "Mein Konto löschen",
      "Are you sure you want to delete your account?": "Bist du sicher, dass du dein Konto löschen möchtest?",
      "This action cannot be undone. We will delete all your data, your photos and any other information you have stored with us.": "Diese Aktion kann nicht rückgängig gemacht werden. Wir werden alle deine Daten, deine Fotos und alle anderen Informationen löschen, die du bei uns gespeichert hast.",
      "We won't be able to refund any payments you have made.": "Wir können keine Zahlungen erstatten, die du geleistet hast.",
      "There is no way to restore your account once it is deleted.": "Es gibt keine Möglichkeit, dein Konto wiederherzustellen, sobald es gelöscht wurde.",
      "Type the email above": "Gib die obige E-Mail-Adresse ein",
      "Type {email} to delete your account": "Gib {email} ein, um dein Konto zu löschen",
      "Cancel": "Abbrechen",
      "Profile | HeadshotPro": "Profil | HeadshotPro",
      "The email you typed does not match your account email.": "Die eingegebene E-Mail-Adresse stimmt nicht mit deiner Konto-E-Mail überein.",
      "Your account has been deleted.": "Dein Konto wurde gelöscht."
    }
  }
</i18n>
