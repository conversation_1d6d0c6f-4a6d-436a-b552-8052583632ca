<template>
  <div>
    <div v-if="title" class="w-full border-b border-gray-100 pb-4 mb-4">
      <h2 class="font-bold text-lg">
        {{ title }}
      </h2>
    </div>
    <div class="flex items-start justify-between space-x-2">
      <img v-if="!isHolidayMode" src="@/assets/img/support-avatar.png" class="w-12 h-12 rounded-full" alt="Support avatar">
      <img v-if="isHolidayMode" src="@/assets/img/christmas-support-avatar.png" class="w-12 h-12 rounded-full" alt="Support avatar">
      <div class="flex flex-col space-y-1 text-xs text-gray-700">
        <template v-if="!isHolidayMode">
          <span v-if="isSupportOnline" class="text-xs font-medium">
            Camilla is currently <span class="text-green-500">answering emails.</span>
          </span>
          <span v-else class="text-xs font-medium">
            <PERSON><PERSON> will be back again on <span class="text-orange-500">{{ nextOnlineTime }}.</span>
          </span>
          <span>You can expect a response from <PERSON><PERSON> within the next <span v-if="isSupportOnline" class="font-medium">1-3 business hours</span><span v-else class="font-medium">24 business hours</span>, but usually she's a little quicker than that. Please be nice to Camilla—she's here to help 🙂</span>
        </template>
        <template v-else>
          <div class="flex items-center space-x-1 text-xs text-left justify-left text-gray-800">
            <IconExclamation class="size-3 text-orange-500" /> <span class="font-bold">Our team is currently on holiday break.</span>
          </div>
          <span>Headshots are still being created, as usual, but our Support team won't be back at the office until {{ formatDate(holidayEndDate) }}.</span>
          <span>Submissions made here will receive a response via email as soon as we return. Priority requests (like refunds) will be continue to be processed within 1 business day.</span>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: 'Support & Feedback'
    }
  },
  computed: {
    isSupportOnline () {
      const now = new Date()
      // Convert local time to GMT+8
      const gmt8Time = new Date(now.getTime() + (now.getTimezoneOffset() * 60000) + (8 * 3600000))

      const hours = gmt8Time.getHours()
      const day = gmt8Time.getDay()

      // Support hours are from 13:00 to 17:00 GMT+8
      const isWeekday = day >= 1 && day <= 5 // Monday (1) to Friday (5)
      const isWorkingHours = hours >= 13 && hours < 17

      return isWeekday && isWorkingHours
    },
    nextOnlineTime () {
      const now = new Date()
      const localTime = now.getTime()
      const localOffset = now.getTimezoneOffset() * 60000
      const utc = localTime + localOffset
      const offset = 8 // GMT+8
      const supportTime = utc + (3600000 * offset)
      const supportDate = new Date(supportTime)

      const day = supportDate.getUTCDay()
      let nextOnline

      if (day >= 5) { // If it's Friday or the weekend, set to Monday
        nextOnline = new Date(supportDate)
        nextOnline.setUTCDate(supportDate.getUTCDate() + ((8 - day) % 7))
        nextOnline.setUTCHours(13, 0, 0, 0) // Set to 13:00 GMT+8
      } else {
        nextOnline = new Date(supportDate)
        nextOnline.setUTCHours(13, 0, 0, 0) // Set to 13:00 GMT+8
        if (supportDate.getUTCHours() >= 16) {
          // If after working hours, set to next day
          nextOnline.setUTCDate(supportDate.getUTCDate() + 1)
        }
      }

      // Format the date to a readable format, e.g., "Monday at 1:00 PM"
      return nextOnline.toLocaleString('en-US', { weekday: 'long', hour: 'numeric', minute: 'numeric', hour12: true, timeZone: 'UTC' })
    }
  }

}
</script>

<style>

</style>
