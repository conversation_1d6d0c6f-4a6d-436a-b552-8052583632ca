<template>
  <section id="examples" class="py-12 bg-white sm:py-16 lg:py-20 xl:py-24 xl:pb-12 lg:pb-10">
    <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
      <div class="max-w-5xl mx-auto text-center">
        <p class="text-lg font-bold text-teal-500">
          {{ $t('Reviews & examples') }}
        </p>
        <h2 class="mt-6 text-2xl font-medium lg:leading-[54px] tracking-tight sm:text-4xl lg:text-5xl text-primary-500">
          <span class=" font-bold text-teal-500">
            {{ $store.state.stats.photos }}</span>
          {{ $t('AI headshots created') }}
          <br class="hidden md:block">
          {{ $t('for') }}
          <span class="font-bold text-yellow-500">{{ $store.state.stats.users }}</span>
          {{ $t('happy customers!') }}
        </h2>
      </div>
      <MarketingLogoCloud class="w-full max-w-2xl mx-auto mt-8" />
      <div class="mt-8 sm:mt-12 lg:mt-16 space-y-4 relative">
        <span class="hidden md:block text-paragraph absolute left-[50%] w-[300px] text-center ml-[-150px] font-cursive top-[48px] text-[14px]">All it takes is just a few selfies</span>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <ShootExample
            v-for="(item, index) in items"
            :key="item.value"
            :index="index"
            :item="item"
            :examples="2"
            :cols="2"
          />
        </div>
      </div>
      <div id="convert-11-v1-b">
        <div class="hidden md:flex items-center justify-center w-full space-x-2 text-sm animate-pulse mt-4">
          <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
          <!-- <span>These photos are not real. All of them were created with our AI headshot generator</span> -->
          <p><span class="font-medium text-brand-600">{{ $t('These photos are not real') }}. </span>{{ $t('They were all created using our AI headshot generator') }}.</p>
          <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
        </div>
        <div v-show="!screenWidth || screenWidth >= 768" class="hidden md:grid grid-cols-2 md:grid-cols-4 gap-2 mt-4">
          <div v-for="item of newReviews.slice(0, 12)" :key="item._id" class="transition group duration-300 relative overflow-hidden rounded-md">
            <div class="bg-teal-600/70 backdrop-blur-md text-white text-[9px] font-medium px-1.5 py-0.5 rounded-md absolute top-2 left-2 z-10 opacity-70 uppercase">
              {{ 'AI GENERATED' }}
            </div>
            <div class="relative  cursor-pointer rounded-lg overflow-hidden" @click="showEnlargedImage(item?.thumbnail || item.image)">
              <ImageDns :src="item?.thumbnail || item.image" :alt="`Professional AI-generated ${item.trigger === 'male' ? 'male' : 'female'} business headshot example created with HeadshotPro`" />
              <div class="hidden group-hover:flex absolute inset-0 bg-black bg-opacity-50 items-center justify-center z-50">
                <span class="text-white text-sm">Click to enlarge</span>
              </div>
            </div>
            <div v-if="item?.review?.quote" class="absolute bg-gradient-to-t h-1/2 flex justify-end flex-col from-black/80 to-transparent text-white p-3 bottom-0 left-0 w-full text-sm  space-y-1">
              <p class="font-bold">
                {{ item?.review?.title }}
              </p>
              <p class="italic text-white/80 text-[10px] leading-[12px]">
                "{{ item?.review?.quote }}"
              </p>
            </div>
          </div>
        </div>

        <!-- <div v-show="!screenWidth || screenWidth >= 768" class="hidden md:grid grid-cols-4 md:grid-cols-6 grid-rows-2 gap-2 mt-6">
          <template v-for="item of oldReviews.slice(0, 18)">
            <div
              :key="item._id"
              class="transition duration-300 relative overflow-hidden"
            >
              <div class="bg-black/70 backdrop-blur-md text-white text-[9px] font-medium px-1.5 py-0.5 rounded-md absolute top-2 left-2 z-10 opacity-70 uppercase">
                {{ (isCreatedAfterAugust21(item.createdAt)) ? 'NEW MODEL' : 'AI GENERATED' }}
              </div>
              <ImageDns :src="item?.thumbnail || item.image" />
              <div v-if="item?.review?.quote" class="absolute bg-gradient-to-t h-1/2 flex justify-end flex-col from-black/80 to-transparent text-white p-3 bottom-0 left-0 w-full text-sm  space-y-1">
                <p class="font-bold">
                  {{ item?.review?.title }}
                </p>
                <p class="italic text-white/80 text-[10px] leading-[12px]">
                  "{{ item?.review?.quote }}"
                </p>
              </div>
            </div>
          </template>
        </div> -->
        <div class="grid md:hidden grid-cols-2 gap-1 mt-4">
          <ImageDns v-for="item of newReviews.slice(0, 4)" :key="item._id" :src="item?.thumbnail || item.image" :alt="`AI-generated professional headshot example - ${item.trigger === 'male' ? 'male' : 'female'} business portrait created with HeadshotPro`" />
        </div>
      </div>
      <div class="hidden lg:grid lg:grid-cols-4 gap-4 mt-4">
        <LandingpageV2ReviewTrustpilot v-for="item of trustPilotReviews.slice(0, 8)" :key="item.id" :item="item" :cap-length="true" />
      </div>
      <div class="grid lg:hidden lg:grid-cols-4 gap-4 mt-4">
        <LandingpageV2ReviewTrustpilot v-for="item of trustPilotReviews.slice(0, 4)" :key="item.id" :item="item" />
      </div>
      <div class="flex items-end justify-center h-full bg-gradient-to-t from-white via-white to-transparent mt-[-100px] z-10 relative pt-24 pb-2">
        <div class="bg-yellow-50 border border-yellow-200 p-2 px-8 rounded-md flex flex-col md:flex-row items-center justify-center space-y-2 md:space-y-0 md:space-x-4 shadow-md">
          <nuxt-link to="/reviews">
            <ButtonWhite>
              <span class="hidden md:inline-flex">{{ $t('View all reviews and examples') }}</span>
              <span class="md:hidden">{{ $t('View all reviews') }}</span>
            </ButtonWhite>
          </nuxt-link>
          <nuxt-link class="hidden md:inline-flex" :to="localePath('/app/add')">
            <ButtonPrimary class="bg-teal-500">
              <span>{{ $t('Create your headshots now') }}</span>
              <IconChevron class="w-4 h-4 text-white/50 ml-2" />
            </ButtonPrimary>
          </nuxt-link>
        </div>
      </div>

      <!-- <div class="w-full relative  mt-4">
        <div class="w-full h-16 absolute top-[-80px] bg-gradient-to-b from-transparent to-white" />
        <div class="flex items-end justify-center h-full">
          <div class="bg-yellow-100 p-2 rounded-md">
            <nuxt-link to="/reviews">
              <ButtonPrimary>
                <span>View all reviews and examples</span>
                <IconChevron class="w-4 h-4 text-white/50 ml-2" />
              </ButtonPrimary>
            </nuxt-link>
          </div>
        </div>
      </div> -->
      <!-- <div class=" p-2 md:p-4 divide-4 relative">
        <div v-masonry="" item-selector=".item" transition-duration="0s" stagger="0s" class="mt-8 sm:mt-12 lg:mt-16">
          <template v-for="(item, index) in tweets.slice(0,4)">
            <div :key="item._id" v-masonry-tile class="item w-1/2 sm:w-1/2 md:w-1/4 lg:w-1/4 pb-2 pr-2 md:pb-4 md:pr-4">
              <ReviewTweet :key="'tweet'+index" :item="item" />
            </div>
          </template>
        </div>
      </div> -->

      <!-- <div class="grid grid-cols-2 gap-4 mt-8 sm:mt-12 lg:grid-cols-4 sm:gap-5 lg:mt-16">
        <ImageDns class="object-cover w-full h-full rounded" :src="require('@/assets/img/work-samples/work-sample-portrait-1.png')" alt="" />
        <ImageDns class="object-cover w-full h-full rounded" :src="require('@/assets/img/work-samples/work-sample-portrait-2.png')" alt="" />
        <ImageDns class="object-cover w-full h-full rounded" :src="require('@/assets/img/work-samples/work-sample-portrait-3.png')" alt="" />
        <ImageDns class="object-cover w-full h-full rounded" :src="require('@/assets/img/work-samples/work-sample-portrait-4.png')" alt="" />
      </div>

      <div class="grid grid-cols-1 gap-4 mt-4 sm:grid-cols-2 lg:grid-cols-3 sm:mt-5 sm:gap-5">
        <ImageDns
          class="object-cover w-full h-full rounded"
          :src="require('@/assets/img/work-samples/work-sample-landscape-1.png')"
          alt=""
        />
        <ImageDns
          class="object-cover w-full h-full rounded"
          :src="require('@/assets/img/work-samples/work-sample-landscape-2.png')"
          alt=""
        />
        <ImageDns
          class="object-cover w-full h-full rounded sm:col-span-2 lg:col-span-1"
          :src="require('@/assets/img/work-samples/work-sample-landscape-3.png')"
          alt=""
        />
      </div> -->
    </div>
    <portal to="modal">
      <div
        v-if="enlargedImage"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 cursor-pointer"
        @click="enlargedImage = null"
      >
        <div class="max-h-[90vh] max-w-[768px] object-contain relative">
          <img :src="enlargedImage" class="max-h-[90vh] max-w-[768px] object-contain" alt="Enlarged view of professional AI-generated headshot created with HeadshotPro">
          <span class="absolute top-2 right-2 text-white text-sm cursor-pointer" @click="enlargedImage = null">
            Close
          </span>
          <div class="absolute text-xs font-medium top-2 left-2 text-white  bg-teal-600/70 backdrop-blur-md px-1.5 py-0.5 rounded-md">
            100% AI generated
          </div>
        </div>
      </div>
    </portal>
  </section>
</template>

<script>
export default {
  props: {
    tweets: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      enlargedImage: null
    }
  },
  computed: {
    reviews () {
      return this.$store.state.reviews
    },
    trustPilotReviews () {
      const longReviews = this.$store.state.trustpilotReviews.filter(review => review.text.length > 100)
      return longReviews
    },
    // filteredReviews () {
    //   // Create a copy of reviews array and sort it
    //   return [...this.reviews].sort((a, b) => {
    //     if (a.review.quote && !b.review.quote) {
    //       return -1
    //     }
    //     if (!a.review.quote && b.review.quote) {
    //       return 1
    //     }
    //     return 0
    //   })
    // },
    oldReviews () {
      // ANy reviews before augst 19th
      return this.reviews.filter(review => !this.isCreatedAfterAugust21(review.createdAt))
    },
    newReviews () {
      const filteredReviews = this.reviews.filter(review =>
        this.isCreatedAfterAugust21(review.createdAt) && review?.review?.frontpage === true
      )

      // Separate reviews by gender
      const maleReviews = filteredReviews.filter(review => review.trigger === 'male')
      const femaleReviews = filteredReviews.filter(review => review.trigger === 'female')

      // Interleave male and female reviews
      const mixedReviews = []
      const maxLength = Math.max(maleReviews.length, femaleReviews.length)
      for (let i = 0; i < maxLength; i++) {
        if (maleReviews[i]) { mixedReviews.push(maleReviews[i]) }
        if (femaleReviews[i]) { mixedReviews.push(femaleReviews[i]) }
      }

      return mixedReviews
    },

    items () {
      // Get first 2
      // If mobile get first
      if (process.client && window.innerWidth < 768) {
        return this.$store.state.examples.slice(1, 2)
      }
      return this.$store.state.examples.slice(0, 2)
    }
  },
  mounted () {
    // console.log(this.reviews)
  },
  methods: {
    showEnlargedImage (image) {
      this.enlargedImage = image
    },
    formatReviewDate (date) {
      const now = new Date()
      const reviewDate = new Date(date)
      const timeDiff = now - reviewDate
      const daysAgo = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
      return daysAgo === 0 ? this.$t('Today') : (daysAgo === 1) ? this.$t('1 day ago') : this.$t('nDaysAgo', { n: daysAgo })
    },
    isCreatedAfterAugust21 (createdAt) {
      const date = new Date(createdAt)
      const august21 = new Date('2024-08-19')
      return date > august21
    }
  }
  // data () {
  //   return {
  //     tweets: []
  //   }
  // },
  // async fetch () {
  //   const tweets = await this.$axios.$get('/reviews/wall-of-love/twitter', { params: { limit: 8 } })
  //   this.tweets = tweets
  // }
}
</script>

<i18n>
  {
    "en": {
      "nDaysAgo": "{n} days ago"
    },
    "es": {
      "Reviews & examples": "Reseñas y ejemplos",
      "AI headshots created": "fotos IA creadas",
      "for": "para",
      "happy customers!": "clientes felices!",
      "These photos are not real": "Estas fotos no son reales",
      "They were all created using our AI headshot generator": "Todas fueron creadas con nuestro generador de fotos de IA",
      "AI Generated": "Generado por IA",
      "View all reviews and examples": "Ver todas las reseñas y ejemplos",
      "Create your headshots now": "Obtén tus fotos ahora",
      "Today": "Hoy",
      "1 day ago": "Hace 1 día",
      "nDaysAgo": "Hace {n} días"
    },
    "de": {
      "Reviews & examples": "Bewertungen & Beispiele",
      "AI headshots created": "KI-Headshots erstellt",
      "for": "für",
      "happy customers!": "zufriedene Kunden!",
      "These photos are not real": "Diese Fotos sind nicht echt",
      "They were all created using our AI headshot generator": "Sie wurden alle mit unserem KI-Headshot-Generator erstellt",
      "AI Generated": "KI-generiert",
      "View all reviews and examples": "Alle Bewertungen & Beispiele ansehen",
      "Create your headshots now": "Jetzt Headshots erstellen",
      "Today": "Heute",
      "1 day ago": "Vor 1 Tag",
      "nDaysAgo": "Vor {n} Tagen"
    }
  }
</i18n>
