<template>
  <footer class="py-12 bg-zinc-900 sm:py-16 lg:pt-20">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
      <div class="w-full  flex justify-between items-start">
        <div class="flex flex-col items-start gap-3">
          <nuxt-link to="/" title="" class="flex">
            <img
              width="181"
              height="28"
              class="h-6 w-auto"
              src="@/assets/img/logo-white.svg"
              alt="HeadshotPro - Professional AI headshot generator for business professionals"
              loading="lazy"
            >
          </nuxt-link>

          <p class="text-base font-medium text-white sm:text-lg">
            {{ $t('Create professional business headshots in minutes with HeadshotPro.') }}
          </p>

          <div class="flex flex-wrap items-center gap-6">
            <div class="flex items-center gap-3">
              <img class="w-auto h-6" src="@/assets/img/trustpilot-stars-5.svg" alt="5-star rating on Trustpilot for HeadshotPro AI headshot generator" loading="lazy">
              <img class="w-auto h-6" src="@/assets/img/logo-trustpilot-white.png" alt="Trustpilot logo - HeadshotPro customer reviews and ratings" loading="lazy">
            </div>

            <div class="flex items-center gap-3">
              <div class="flex -space-x-2 overflow-hidden">
                <img class="inline-block w-6 h-6 rounded-full ring-2 ring-gray-900" src="@/assets/img/avatar-1.jpg" alt="HeadshotPro customer testimonial avatar - Professional headshot example" loading="lazy">
                <img class="inline-block w-6 h-6 rounded-full ring-2 ring-gray-900" src="@/assets/img/avatar-2.jpg" alt="HeadshotPro customer testimonial avatar - Business professional headshot" loading="lazy">
                <img class="inline-block w-6 h-6 rounded-full ring-2 ring-gray-900" src="@/assets/img/avatar-3.jpg" alt="HeadshotPro customer testimonial avatar - Corporate headshot example" loading="lazy">
              </div>
              <p class="text-base font-normal text-white -mt-0.5">
                {{ $t('Trusted by') }} <span class="font-bold">{{ $store.state.stats.users }}+</span> {{ $t('happy customers') }}
              </p>
            </div>
          </div>
        </div>
        <div class="flex items-center gap-3 justify-end">
          <a href="https://trust.oneleet.com/headshotpro" target="_blank" rel="noopener noreferrer">
            <img src="https://badges.oneleet.com/badge/05532cf0-0fb3-4dbe-9507-8642a6434fdb?dark=true" class="w-[80px]" alt="OneLeet security compliance badge - HeadshotPro data protection certification">
          </a>
        </div>
      </div>

      <hr class="mt-8 border-white/10 sm:mt-12">

      <div class="grid grid-cols-2 gap-8 mt-8 sm:gap-12 sm:grid-cols-3 xl:grid-cols-6 sm:mt-12">
        <div>
          <h6 class="text-base font-bold text-white">
            {{ $t('Links') }}
          </h6>
          <ul class="mt-6 space-y-5 text-base font-normal text-white">
            <template v-for="nav in mainNav">
              <li v-if="!nav.children" :key="nav.url">
                <nuxt-link :to="nav.url" title="" class="transition-all duration-150 hover:opacity-80">
                  {{ nav.title }}
                </nuxt-link>
              </li>
            </template>
            <li>
              <nuxt-link to="/tools/free-headshot-generator" title="" class="transition-all duration-150 hover:opacity-80">
                Free Headshot Generator
              </nuxt-link>
            </li>
          </ul>
        </div>

        <div>
          <h6 class="text-base font-bold text-white">
            {{ $t('Support') }}
          </h6>
          <ul class="mt-6 space-y-5 text-base font-normal text-white">
            <li>
              <nuxt-link to="/contact" title="" class="transition-all duration-150 hover:opacity-80">
                Contact us
              </nuxt-link>
            </li>
            <li>
              <nuxt-link to="/affiliate" title="" class="transition-all duration-150 hover:opacity-80">
                Affiliate
              </nuxt-link>
            </li>
            <li>
              <nuxt-link to="/api" title="" class="transition-all duration-150 hover:opacity-80">
                API
              </nuxt-link>
            </li>
            <li>
              <nuxt-link to="/best-ai-headshot-generators" title="" class="transition-all duration-150 hover:opacity-80">
                Compare HeadshotPro
              </nuxt-link>
            </li>
          </ul>
        </div>

        <div>
          <h6 class="text-base font-bold text-white">
            {{ $t('Legal') }}
          </h6>
          <ul class="mt-6 space-y-5 text-base font-normal text-white">
            <li v-for="nav in legal" :key="nav.url">
              <template v-if="nav.url.includes('http')">
                <a :href="nav.url" :title="nav.title" class="transition-all duration-150 hover:opacity-80">
                  {{ nav.title }}
                </a>
              </template>
              <template v-else>
                <nuxt-link :to="nav.url" :title="nav.title" class="transition-all duration-150 hover:opacity-80">
                  {{ nav.title }}
                </nuxt-link>
              </template>
            </li>
          </ul>
        </div>

        <div>
          <h6 class="text-base font-bold text-white">
            {{ $t('Free Tools') }}
          </h6>
          <ul class="mt-6 space-y-5 text-base font-normal text-white">
            <li v-for="nav in tools" :key="nav.url">
              <nuxt-link :title="nav.title" :to="nav.url" class="transition-all duration-150 hover:opacity-80">
                {{ nav.title }}
              </nuxt-link>
            </li>
          </ul>
        </div>

        <div>
          <h6 class="text-base font-bold text-white">
            {{ $t('Headshot Types') }}
          </h6>
          <ul class="mt-6 space-y-5 text-base font-normal text-white">
            <li v-for="nav in headshotTypes" :key="nav.url">
              <nuxt-link :title="nav.title" :to="nav.url" class="transition-all duration-150 hover:opacity-80">
                {{ nav.title }}
              </nuxt-link>
            </li>
          </ul>
        </div>

        <div>
          <h6 class="text-base font-bold text-white">
            {{ $t('FAQ') }}
          </h6>
          <ul class="mt-6 space-y-5 text-base font-normal text-white">
            <li v-for="nav in faq" :key="nav.url">
              <nuxt-link :title="nav.title" :to="nav.url" class="transition-all duration-150 hover:opacity-80">
                {{ nav.title }}
              </nuxt-link>
            </li>
          </ul>
        </div>
      </div>

      <hr class="mt-8 border-white/10 sm:mt-12">

      <div class="mt-8 sm:mt-12 md:flex md:items-center md:justify-between md:gap-6">
        <div class="flex flex-wrap items-center gap-5">
          <nuxt-link v-for="nav in faq" :key="nav.url" :title="nav.title" :to="nav.url" class="text-base font-normal text-white transition-all duration-150 hover:opacity-80">
            {{ nav.title }}
          </nuxt-link>
        </div>

        <p class="mt-12 text-base font-normal text-white md:mt-0">
          © 2021 - {{ new Date().getFullYear() }}, {{ $t('All Rights Reserved') }}
        </p>
      </div>
    </div>
  </footer>
</template>

<script>
const content = require('@/pages/headshot-types/content.json')
export default {
  data () {
    return {
      legal: [
        { title: 'Refund', url: '/refund' },
        { title: 'Terms & Conditions', url: '/legal/terms-and-conditions' },
        { title: 'Privacy Policy', url: '/legal/privacy-policy' },
        { title: 'Sub-processors', url: '/legal/sub-processors' },
        { title: 'Security Policy', url: '/legal/security-policy' },
        { title: 'Trust Page', url: 'https://trust.oneleet.com/headshotpro' },
        { title: 'Helpdesk', url: 'https://headshotpro.crisp.help/en/' }
      ],
      faq: [
        { title: 'About us', url: '/author/danny-postma' },
        { title: 'Blog', url: '/blog' },
        { title: 'Jobs', url: '/jobs' }
      ],
      support: [
        { title: 'Affiliate', url: '/affiliate' },
        { title: 'API', url: '/api' },
        { title: 'Compare HeadshotPro', url: '/best-ai-headshot-generators' }
      ]
    }
  },
  computed: {
    headshotTypes () {
      return this.$store.state.navigation.headshotTypes
    },
    mainNav () {
      return this.$store.state.navigation.left
    },
    tools () {
      return this.$store.state.navigation.right.filter(navItem => navItem.title === 'Tools')[0]?.children || []
    },
    typeNav () {
      const items = []
      // Loop through object
      for (const [key, value] of Object.entries(content)) {
        items.push({
          title: `${this.slugToTitle(key)} Headshots`,
          url: `/headshot-types/${value.keyword}-headshots`
        })
      }
      return items
      // return content.map((item) => {
      //   return {
      //     url: `/headshot-types/${item.keyword}-headshots`,
      //     title: `${item.keyword} Headshots`
      //   }
      // })
    }
  }
}
</script>

<style></style>
