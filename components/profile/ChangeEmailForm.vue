<template>
  <LoadingWrapper :is-loading="isSubmitting">
    <div class="space-y-4">
      <Input v-model="email" :placeholder="email" type="email" label="New email" />
      <Input v-model="emailRepeat" :placeholder="emailRepeat" type="email" label="New email repeat" />
      <Input v-model="password" label="Password" type="password" />
      <ButtonDark size="sm" @click="updateEmail">
        Change email
      </ButtonDark>
      <AlertAttention v-if="hasError" :description="errorMessage" />
      <AlertSuccess v-if="isSuccess" :description="successMessage" />
    </div>
  </LoadingWrapper>
</template>

<script>
import firebase from 'firebase/compat/app'
import 'firebase/compat/firestore'
import 'firebase/compat/auth'

export default {
  data () {
    return {
      email: null,
      emailRepeat: null,
      password: null,
      isSubmitting: false,
      hasError: false,
      errorMessage: '',
      isSuccess: false,
      successMessage: ''
    }
  },
  methods: {
    async updateEmail () {
      this.isSubmitting = true
      const user = this.$fire.auth.currentUser
      const credential = firebase.auth.EmailAuthProvider.credential(
        user.email,
        this.password
      )
      try {
        await user.reauthenticateWithCredential(credential)
      } catch (e) {
        console.error('error', e.code, e.message)
        this.errorMessage = e.message
        this.isSubmitting = false
        this.hasError = true
        return
      }
      try {
        await this.$fire.auth.currentUser.updateEmail(this.email)
        this.isSuccess = true
        this.successMessage = 'Email successfully changed.'
        this.$axios.$put('/users/profile', { email: this.email })
      } catch (e) {
        console.error('error', e.code, e.message)
        this.errorMessage = e.message
        this.isSubmitting = false
        this.hasError = true
        return
      }

      try {
        await this.$fire.auth.currentUser.sendEmailVerification()
      } catch (e) {
        this.errorMessage = e.message
        this.isSubmitting = false
        this.hasError = true
        console.error('error', e.code, e.message)
      }
    }
  }
}
</script>

<style>

</style>
