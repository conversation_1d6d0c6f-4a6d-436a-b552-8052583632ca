<template>
  <LoadingWrapper :is-loading="isSubmitting">
    <div class="space-y-4">
      <Input v-model="currentPassword" type="password" label="Current password" />
      <Input v-model="newPassword" type="password" label="New password" />
      <Input v-model="newPasswordRepeat" type="password" label="New password repeat" />
      <ButtonDark size="sm" @click="updatePassword">
        Change password
      </ButtonDark>
      <AlertAttention v-if="hasError" :description="errorMessage" />
      <AlertSuccess v-if="isSuccess" :description="successMessage" />
    </div>
  </LoadingWrapper>
</template>

<script>
import firebase from 'firebase/compat/app'
import 'firebase/compat/firestore'
import 'firebase/compat/auth'

export default {
  data () {
    return {
      currentPassword: null,
      newPassword: null,
      newPasswordRepeat: null,
      isSubmitting: false,
      hasError: false,
      errorMessage: '',
      isSuccess: false,
      successMessage: ''
    }
  },
  methods: {
    async updatePassword () {
      if (!this.currentPassword || this.currentPassword.length === 0) {
        this.errorMessage = 'Fill in your current password.'
        this.hasError = true
        return
      }

      if (!this.newPassword || this.newPassword.length === 0 || !this.newPasswordRepeat || this.newPasswordRepeat.length === 0) {
        this.errorMessage = 'Choose a new password.'
        this.hasError = true
        return
      }

      if (this.newPassword !== this.newPasswordRepeat) {
        this.errorMessage = 'New passwords do not match.'
        this.hasError = true
        return
      }

      this.isSubmitting = true
      const user = this.$fire.auth.currentUser
      const credential = firebase.auth.EmailAuthProvider.credential(
        user.email,
        this.currentPassword
      )
      try {
        await user.reauthenticateWithCredential(credential)
      } catch (e) {
        console.error('error', e.code, e.message)
        this.isSubmitting = false
        this.hasError = true
        this.errorMessage = e.message
        return
      }
      try {
        await this.$fire.auth.currentUser.updatePassword(
          this.newPassword
        )
        this.isSuccess = true
        this.isSubmitting = false
        this.successMessage = 'Password successfully changed.'
      } catch (e) {
        console.error('error', e.code, e.message)
        this.isSubmitting = false
        this.hasError = true
        this.errorMessage = e.message
      }
    }
  }
}
</script>

<style>

</style>
