<template>
  <LoadingWrapper :is-loading="isSubmitting">
    <div class="space-y-4">
      <Input v-model="displayName" type="email" label="New name" />
      <ButtonDark size="sm" @click="updateName">
        Change name
      </ButtonDark>
      <AlertAttention v-if="hasError" :description="errorMessage" />
      <AlertSuccess v-if="isSuccess" :description="successMessage" />
    </div>
  </LoadingWrapper>
</template>

<script>
export default {
  data () {
    return {
      displayName: null,
      isSubmitting: false,
      hasError: false,
      errorMessage: '',
      isSuccess: false,
      successMessage: ''
    }
  },
  methods: {
    async updateName () {
      this.isSubmitting = true
      try {
        await this.$fire.auth.currentUser.updateProfile({
          displayName: this.displayName
        })
        this.isSuccess = true
        this.isSubmitting = false
        this.successMessage = 'Name successfully changed.'
        this.$axios.$put('/users/profile', { displayName: this.displayName })
        this.setupUserFirebaseData()
      } catch (e) {
        console.error('error', e.code, e.message)
        this.errorMessage = e.message
        this.isSubmitting = false
        this.hasError = true
      }
    }
  }
}
</script>

<style>

</style>
