<template>
  <div
    class="px-4 py-8 border-t border-gray-100 lg:border-t-0 lg:border-l bg-gray-50 lg:px-8 lg:py-12 xl:pl-12"
  >
    <div class="space-y-5">
      <div v-if="pendingPhotos.length > 0" class="px-4 py-5 bg-gray-100 border border-gray-200 rounded-lg">
        <div class="flex items-center gap-4">
          <svg
            class="w-5 h-5 text-gray-500 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          <p class="text-sm font-medium text-gray-600">
            {{ $t('Checking your photos, hang on!') }}
          </p>
        </div>
        <div class="grid grid-cols-5 md:grid-cols-6 gap-2.5 mt-4 lg:pr-20 xl:pr-32">
          <PostcheckoutUploadedPhoto v-for="photo in pendingPhotos" :key="photo.md5 + '_' + photo.status" :photo="photo" />
        </div>
      </div>

      <template v-if="goodPhotos.length > 0 || pendingPhotos.length > 0">
        <div class="flex items-center justify-between lg:hidden">
          <p class="text-base font-bold tracking-tight text-primary-500">
            {{ $t('Review photos') }}
          </p>

          <PostcheckoutProgressIndicator :current="goodPhotos.length" :total="minimumPhotos" />
        </div>
        <!-- FACE PHOTOS -->
        <div class="px-4 py-5 border border-green-200 rounded-lg bg-green-50">
          <div class="flex items-center justify-between">
            <div class="flex items-center justify-start gap-2">
              <p class="text-sm font-medium tracking-tight text-green-800">
                {{ $t('Good face photos') }}
              </p>
              <Tooltip v-if="goodFacePhotos.length > (minimumPhotos - minimumFullBodyPhotos)" :info="`You've uploaded more photos than needed. Delete atleast ${goodFacePhotos.length - (minimumPhotos - minimumFullBodyPhotos) } face photos or we will delete randomly for you.`" placement="left">
                <template #icon>
                  <IconExclamation class="w-4 h-4 text-orange-500" />
                </template>
              </Tooltip>
            </div>

            <PostcheckoutProgressIndicator class="hidden md:block" :current="goodFacePhotos.length" :total="minimumPhotos" />
          </div>

          <hr class="mt-4 border-green-200">

          <div class="grid grid-cols-5 md:grid-cols-6 gap-2.5 mt-4 lg:pr-20 xl:pr-32">
            <PostcheckoutUploadedPhoto v-for="photo in goodFacePhotos" :key="photo.md5 + '_' + photo.status" :photo="photo" />
          </div>
        </div>
        <!-- END FACE PHOTOS -->
        <!-- FULL BODY PHOTOS -->
        <div class="px-4 py-5 border border-green-200 rounded-lg bg-green-50">
          <div class="flex items-center justify-between">
            <div class="flex items-center justify-start gap-2">
              <p class="text-sm font-medium tracking-tight text-green-800">
                {{ $t('Good full body photos') }}
              </p>
            </div>

            <PostcheckoutProgressIndicator class="hidden md:block" :current="goodFullBodyPhotos.length" :total="minimumFullBodyPhotos" />
          </div>

          <hr class="mt-4 border-green-200">

          <div v-if="goodFullBodyPhotos.length > 0" class="grid grid-cols-5 md:grid-cols-6 gap-2.5 mt-4 lg:pr-20 xl:pr-32">
            <PostcheckoutUploadedPhoto v-for="photo in goodFullBodyPhotos" :key="photo.md5 + '_' + photo.status" :photo="photo" />
          </div>
          <span v-else>
            <p class="text-sm text-gray-500 py-2">
              Full body photos are optional, but we recommend uploading at least two for higher quality headshots.
            </p>
          </span>
        </div>
        <!-- END FULL BODY PHOTOS -->
      </template>

      <div v-if="badPhotos.length > 0" class="px-4 py-5 border border-red-200 rounded-lg bg-red-50">
        <div class="flex items-center justify-between">
          <p class="text-sm font-medium tracking-tight text-red-800">
            {{ $t('Bad photos') }}
          </p>

          <button type="button" class="text-sm font-medium text-red-800 underline" @click="openRequirementsModal">
            {{ $t('Show photo requirements') }}
          </button>
        </div>
        <p class="mt-1 text-sm font-normal text-red-800">
          {{ $t('We don\'t think these photos will result in headshots you\'d be happy with.') }}
        </p>

        <hr class="mt-4 border-red-200">

        <div class="grid grid-cols-5 md:grid-cols-6 gap-2.5 mt-4 lg:pr-20 xl:pr-32">
          <PostcheckoutUploadedPhoto v-for="photo in badPhotos" :key="photo.md5 + '_' + photo.status" :photo="photo" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PostcheckoutMixin from '../../mixins/PostcheckoutMixin'
export default {
  mixins: [PostcheckoutMixin]
}
</script>

<i18n>
  {
    "en": {
      "Checking your photos, hang on!": "Checking your photos, hang on!",
      "Review photos": "Review photos",
      "Good photos": "Good photos",
      "Bad photos": "Bad photos",
      "Show photo requirements": "Show photo requirements",
      "We don't think these photos will result in headshots you'd be happy with.": "We don't think these photos will result in headshots you'd be happy with."
    },
    "es": {
      "Checking your photos, hang on!": "¡Estamos revisando tus fotos, espera!",
      "Review photos": "Revisar fotos",
      "Good photos": "Buenas fotos",
      "Bad photos": "Malas fotos",
      "Show photo requirements": "Mostrar requisitos para las fotos",
      "We don't think these photos will result in headshots you'd be happy with.": "No creemos que estas fotos resulten en fotos con las que estarías contento."
    },
    "de": {
      "Checking your photos, hang on!": "Wir prüfen deine Fotos, einen Moment!",
      "Review photos": "Fotos überprüfen",
      "Good photos": "Gute Fotos",
      "Bad photos": "Schlechte Fotos",
      "Show photo requirements": "Foto-Anforderungen anzeigen",
      "We don't think these photos will result in headshots you'd be happy with.": "Wir denken nicht, dass diese Fotos zu Bewerbungsfotos führen, mit denen du zufrieden wärst."
    }
  }
</i18n>
