<template>
  <Card data-testid="upsell-card" class="!p-3">
    <div v-if="upsellPhotos.length > 0" class="flex items-center justify-start -space-x-3 mb-2">
      <template v-for="photo in upsellPhotos">
        <ImageDns :key="photo._id" :src="photo.thumbnail || photo.image" class="w-10 h-10 rounded-full shadow-upsell object-cover" />
      </template>
    </div>
    <p class="text-[17px] text-black font-bold">
      Unlock more photos
    </p>
    <p class="text-sm text-black/45 mt-2">
      Loved your photos? Unlock more with any backdrop and outfit combination you like.
    </p>
    <ButtonWhite class="w-full mt-4" size="sm" @click="goToUpsell">
      Unlock now
    </ButtonWhite>
  </Card>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  computed: {
    canUseStudio () {
      // return false
      if (this.isTeamMember) { return false }
      if (this.$store?.state?.results?.item?.images?.filter(image => image.likedStatus === 'favorite').length < 4) {
        return false
      }
      if (this.item?.meta?.canUseStudio) {
        return true
      }
      return false
    },
    gender () {
      return this.item?.trigger || 'male'
    },
    activePhotos () {
      return this.item?.images?.filter(photo => !photo?.likedStatus || photo?.status === 'active') || []
    },
    favoritePhotos () {
      return this.activePhotos.filter(photo => photo?.likedStatus === 'favorite') || []
    },
    dislikedPhotos () {
      return this.activePhotos.filter(photo => photo?.likedStatus === 'dud') || []
    },
    upsellPhotos () {
      return [...this.favoritePhotos, ...this.dislikedPhotos].slice(0, 3)
    }
  },
  methods: {
    goToUpsell () {
      this.$router.push(`/app/results/${this.item._id}/unlock-more?step=1`)
    }
  }
}
</script>

<style scoped>
.scrolling-container {
  width: 100%;
  overflow: hidden;
}

.scrolling-content {
  display: flex;
  width: 200%; /* Two images */
  animation: scroll 10s linear infinite;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%); /* Scroll one image width */
  }
}
</style>
