<template>
  <div class="bg-white border border-gray-200 p-4 relative group">
    <ToolsTeamPagePartialsFavoritePicture
      v-if="favoritePictures?.length > 0"
      :initial-value="styleImage"
      image-class="w-full aspect-[9/12] object-cover"
      :is-editable="editable"
      :favorite-pictures="favoritePictures"
      @update="updatePicture"
    />
    <ToolsTeamPagePartialsPicture
      v-else
      :initial-value="styleImage"
      image-class="w-full aspect-[9/12] object-cover"
      :is-editable="editable"
      @update="updatePicture"
    />
    <ToolsTeamPagePartialsText
      :initial-value="member.role"
      placeholder="Member role"
      :is-editable="editable"
      text-class="text-gray-600 placeholder-gray-600"
      @update="updateRole"
    />
    <ToolsTeamPagePartialsText
      :initial-value="member.name"
      placeholder="Member name"
      :is-editable="editable"
      text-class="text-lg font-bold text-black placeholder-black"
      @update="updateName"
    />
    <ToolsTeamPagePartialsDeleteButton
      v-if="editable && member.id"
      class="absolute right-0 top-0 p-2 text-white bg-black bg-opacity-50 rounded-bl-md opacity-0 group-hover:opacity-100 transition-opacity duration-200"
      :team-id="teamId"
      :member-id="member.id"
      @deleted-member="deletedMember"
    />
  </div>
</template>

<script>
import TeamPageTeamItemMixin from '../../../../../mixins/tools/TeamPage/TeamPageTeamItemMixin'
export default {
  mixins: [TeamPageTeamItemMixin]
}
</script>
