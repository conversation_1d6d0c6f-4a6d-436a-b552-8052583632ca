<template>
  <main class="flex flex-col flex-1">
    <!-- START WRAPPER -->
    <!-- START MAIN -->

    <!-- START COMPARISON -->
    <section id="how-it-works" class="bg-white py-12 sm:py-16 lg:py-20 xl:py-24">
      <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
        <div class="text-left md:text-center">
          <div class="flex items-center justify-start gap-x-3 md:justify-center">
            <img class="h-4 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="4.5-star rating on Trustpilot for HeadshotPro AI headshot generator" loading="lazy">
            <img class="h-4 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="Trustpilot logo - HeadshotPro customer reviews platform" loading="lazy">
          </div>
          <h2 class="max-w-2xl mx-auto mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-[42px] lg:leading-[48px]">
            {{ $t('How Your Selfies Become Professional Headshots') }}
          </h2>
          <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-2xl lg:text-xl">
            {{ $t('Save hundreds of dollars and hours of time by using HeadshotPro to generate your new favorite professional headshots.') }}
          </p>
        </div>

        <div class="mt-8 gap-6 sm:mt-12 md:flex md:justify-center">
          <div class="w-full rounded-lg border border-primary-500/15 bg-white p-6 md:p-8 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] lg:max-w-lg">
            <img class="w-full rounded-lg ring-1 ring-gray-200" src="@/assets/img/landing-page/with-headshotpro.png" alt="HeadshotPro AI headshot generation process - Get professional headshots in 2 hours with just selfies" loading="lazy">

            <div class="mt-6 flex items-center gap-2">
              <p class="text-xl font-bold text-primary-500">
                {{ $t('With') }}
              </p>
              <img class="-mb-0.5 h-6 w-auto" src="@/assets/img/logo.svg" alt="HeadshotPro logo - Professional AI headshot generator" loading="lazy">
            </div>

            <ul class="mt-6 space-y-4 md:space-y-8">
              <li class="flex items-start gap-2 md:gap-4">
                <img class="hidden size-8 shrink-0 sm:block" src="@/assets/img/landing-page/icon-profile.svg" alt="Upload photos icon - Step 1 of HeadshotPro process" loading="lazy">
                <div class="min-w-0 flex-1">
                  <p class="text-lg font-bold leading-none tracking-tighter text-primary-500">
                    {{ $t('1. Upload your photos') }}
                    <span class="font-normal hidden md:inline-flex">{{ $t('(5 minutes)') }}</span>
                  </p>
                  <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                    {{ $t('Use your favorite existing photos or take fresh selfies on the spot.') }}
                  </p>
                </div>
              </li>

              <li class="flex items-start gap-4">
                <img class="hidden size-8 shrink-0 sm:block" src="@/assets/img/landing-page/icon-magic.svg" alt="AI magic icon - Step 2 of HeadshotPro process" loading="lazy">
                <div class="min-w-0 flex-1">
                  <p class="text-lg font-bold leading-none tracking-[-0.056px] text-primary-500">
                    {{ $t('2. Let our AI work its magic') }}
                    <span class="font-normal hidden md:inline-flex">{{ $t('(1-2 hours)') }}</span>
                  </p>
                  <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                    {{ $t('Our AI will pull your most photogenic qualities from the photos you uploaded.') }}
                  </p>
                </div>
              </li>

              <li class="flex items-start gap-4">
                <img class="hidden size-8 shrink-0 sm:block" src="@/assets/img/landing-page/icon-user.svg" alt="Download headshots icon - Step 3 of HeadshotPro process" loading="lazy">
                <div class="min-w-0 flex-1">
                  <p class="text-lg font-bold leading-none tracking-[-0.056px] text-primary-500">
                    {{ $t('3. Download your favorites') }}
                    <span class="font-normal hidden md:inline-flex">{{ $t('(5 minutes)') }}</span>
                  </p>
                  <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                    {{ $t('That was easy! Download your keepers and enjoy your new professional photos.') }}
                  </p>
                </div>
              </li>
            </ul>
          </div>

          <div class="relative hidden min-h-full w-px shrink-0 bg-[#92A0B5] bg-opacity-45 md:block">
            <span class="absolute left-1/2 top-1/2 w-5 -translate-x-1/2 -translate-y-1/2 bg-[#F5F5F5] py-2.5 text-center text-sm font-medium tracking-[-0.3px] text-slate-400">vs</span>
          </div>

          <div class="mt-4 w-full rounded-lg border border-primary-500/15 bg-white p-8 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] md:mt-0 lg:max-w-lg">
            <img class="w-full rounded-lg ring-1 ring-gray-200" src="@/assets/img/landing-page/physical-photoshoot.png" alt="Traditional photography studio process - Physical photoshoots take days with multiple steps compared to HeadshotPro" loading="lazy">

            <p class="mt-5 text-xl font-bold text-primary-500">
              {{ $t('Physical photoshoot') }}
            </p>

            <ul class="mt-4 space-y-1 md:space-y-3 text-base font-normal tracking-[-0.056px] text-paragraph">
              <li class="flex items-center gap-2.5">
                <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                </svg>
                {{ $t('Find a photographer you like') }}
              </li>

              <li class="flex items-center gap-2.5">
                <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                </svg>
                {{ $t('Contact them and wait for a reply') }}
              </li>

              <li class="flex items-center gap-2.5">
                <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                </svg>
                {{ $t("Decide on a date and time you're both available") }}
              </li>

              <li class="flex items-center gap-2.5">
                <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                </svg>
                {{ $t('Find the right clothing to wear to the photo shoot') }}
              </li>

              <li class="flex items-center gap-2.5">
                <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                </svg>
                {{ $t('Get in your car and drive to the photo studio') }}
              </li>

              <li class="flex items-center gap-2.5">
                <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                </svg>
                {{ $t('Pose for your photos and pick your favorite shots') }}
              </li>

              <li class="flex items-center gap-2.5">
                <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                </svg>
                {{ $t('Wait for the photographer to send you the photos') }}
              </li>

              <li class="flex items-center gap-2.5">
                <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                </svg>
                {{ $t('Update your professional profiles with your new photos') }}
              </li>
            </ul>
          </div>
        </div>

        <div class="relative mt-8 text-center sm:mt-12">
          <nuxt-link
            :to="`/auth/login?redirect=${encodeURIComponent(localePath('/app'))}`"
            title=""
            class="inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-primary-600 bg-primary-500 px-6 pb-3.5 pt-2.5 text-lg font-bold leading-6 text-white shadow-[0_0px_24px_0px_rgba(0,0,0,0.25)] transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
            role="button"
          >
            <span class="hidden md:inline-flex">{{ $t('Create your AI headshots within 2 hours') }}</span>
            <span class="md:hidden">{{ $t('Create your headshots') }}</span>
          </nuxt-link>
          <div class="w-full flex items-center justify-center mt-6">
            <client-only>
              <LandingpageRandomTrustpilotReview class="mx-auto" />
            </client-only>
          </div>

          <div class="absolute left-auto top-0 hidden gap-4 lg:flex lg:translate-x-8 lg:items-center xl:translate-x-40">
            <p class="rotate-[-12deg] py-4 text-right font-cursive text-base leading-4 tracking-[-0.056px] text-paragraph">
              {{ $t('Same day results!') }}
            </p>
            <svg class="-mt-12 h-12 w-auto text-paragraph" viewBox="0 0 56 51" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M47.1541 44.8739C44.8708 46.3571 42.6765 47.8339 40.156 49.466C41.3327 50.3929 42.7167 50.2496 43.8436 49.641C47.2242 47.8151 50.6146 45.9006 53.8566 43.7329C55.7149 42.5001 55.6452 40.7462 53.9247 39.9462C51.0771 38.5676 48.1207 37.3728 45.1742 36.0892C45.0061 36.0133 44.7985 35.8963 44.6601 35.9502C44.1164 36.0772 43.6024 36.334 43.0488 36.5497C43.1775 36.9802 43.1084 37.6008 43.3952 37.8C44.3742 38.5213 45.4124 39.1064 46.4012 39.7389C46.9352 40.0964 47.479 40.3651 47.8648 41.2609C46.8367 41.3787 45.7591 41.544 44.731 41.6618C23.5551 43.1825 6.96224 30.8738 3.20946 11.047C2.74404 8.4859 2.59496 5.85814 2.26794 3.24315C2.14903 2.32823 1.98071 1.46082 1.8618 0.545898C1.6443 0.517543 1.41693 0.577842 1.19943 0.549487C0.873343 1.1006 0.319895 1.71201 0.300293 2.28508C0.132791 4.18802 -0.0347098 6.09095 0.0647132 7.97471C0.979821 26.7301 14.2812 41.2539 33.3427 44.0627C36.9909 44.5923 40.8465 44.4473 44.5836 44.5747C45.3745 44.6059 46.1357 44.5072 46.9365 44.4497C47.065 44.4845 47.1342 44.6554 47.1541 44.8739Z"
              />
            </svg>
          </div>
        </div>
      </div>
    </section>
    <!-- END COMPARISON -->

    <!-- <LandingpageV2Versus v-if="experimentIsTest('24-versus-chatgpt-and-canvas-lp-section')" /> -->

    <LandingpageV2AttireAndBackdrop />

    <!-- START FEATURES -->
    <LandingpageV2Features />
    <!-- END FEATURES -->

    <!-- START PRICING -->
    <section id="pricing" class="bg-[#F8FCFF] py-12 sm:py-16 lg:py-20 xl:py-24">
      <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
        <div class="relative mx-auto max-w-2xl text-left md:text-center">
          <div class="flex flex-row items-start md:items-center w-full justify-center gap-3">
            <TrustpilotRating />
          </div>
          <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-[42px] lg:leading-[48px]">
            {{ $t('Professional headshots for 8x less than a physical photo shoot') }}
          </h2>
          <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-2xl lg:text-lg">
            {{ $t('The average cost of professional headshots in the United States is') }}
            <a target="_blank" href="/blog/how-much-does-a-headshot-cost" class="underline">$232.50*.</a>
            {{ $t('Our packages start from $29.') }}
          </p>

          <div class="absolute left-auto top-0 hidden -translate-y-24 translate-x-6 flex-col items-end gap-4 lg:flex">
            <p class="rotate-[12deg] font-cursive leading-4 tracking-[-0.056px] text-paragraph text-[12px]">
              {{ $t('We won\'t let you leave without at least') }}
              <br>
              {{ $t('a handful of good headshots') }}
            </p>
            <svg class="h-12 w-auto -translate-x-24 text-paragraph" viewBox="0 0 36 51" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M29.1188 47.8875C27.1192 48.4038 25.1874 48.9399 22.9811 49.5058C23.6782 50.5531 24.7413 50.827 25.7038 50.668C28.5914 50.1911 31.5038 49.648 34.3552 48.867C35.9876 48.4269 36.2813 47.0455 35.1526 45.9455C33.2952 44.0824 31.3203 42.3316 29.3703 40.5148C29.2596 40.4091 29.1274 40.2605 29.0133 40.2638C28.5817 40.2111 28.1468 40.2675 27.6904 40.2809C27.7017 40.651 27.5277 41.1137 27.7029 41.3482C28.2926 42.1808 28.9536 42.9241 29.5682 43.6905C29.897 44.1167 30.2506 44.4768 30.3625 45.2797C29.5705 45.0851 28.7323 44.9135 27.9403 44.7189C11.8074 40.0059 1.82763 25.8296 2.93039 9.38873C3.08729 7.27041 3.49387 5.18835 3.76488 3.06669C3.85634 2.32312 3.90149 1.60269 3.99295 0.859119C3.83591 0.776563 3.65403 0.760107 3.49699 0.677551C3.14453 1.01475 2.61019 1.3355 2.48256 1.77507C1.98217 3.20615 1.48178 4.63724 1.18475 6.12773C-1.82848 20.9468 5.2536 35.9272 18.9521 43.4136C21.5754 44.8402 24.4868 45.8008 27.2558 46.9398C27.8411 47.1841 28.4296 47.3194 29.0397 47.4976C29.129 47.5604 29.1471 47.7124 29.1188 47.8875Z"
              />
            </svg>
          </div>
        </div>

        <div class="mt-4 flex flex-col items-start md:items-center justify-start md:justify-center gap-4 sm:mt-6  sm:gap-4">
          <div class="flex flex-col items-start md:items-center gap-2">
            <p class="hidden md:flex -mt-0.5 text-sm font-normal text-[#474368]/80 gap-0.5">
              <span class="hidden md:inline-flex font-bold">18M</span>
              <span class="hidden md:inline-flex"> {{ $t('headshots created for') }}</span>
              <span class="font-bold">{{ $store.state.stats.users }}+</span>
              {{ $t('happy customers') }}
            </p>

            <div class="max-w-[100%] w-full md:w-[600px] mx-auto overflow-hidden relative">
              <div class="absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-[#F8FCFF] md:from-[#F8FCFF] to-transparent z-10" />
              <div class="absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-[#F8FCFF] md:from-[#F8FCFF] to-transparent z-10" />
              <img
                src="@/assets/img/logo-cloud-horizontal.png"
                class="w-[240%] max-w-[240%] grayscale opacity-60 animate-scroll"
                alt="Fortune 500 companies and leading businesses trust HeadshotPro for professional AI headshots"
              >
            </div>
          </div>
        </div>

        <LandingpageV2PricingTiers class="mt-8" />
        <hr class="max-w-2xl mx-auto mt-6">

        <div class="max-w-2xl mx-auto mt-8">
          <MarketingLogoCloud />
        </div>

        <!-- <LandingpageV2PricingTiersBottomTestimonials /> -->

        <div class="hidden md:grid md:grid-cols-3 gap-4 mt-8">
          <client-only>
            <LandingpageRandomTrustpilotReview v-for="i in 3" :key="i" />
          </client-only>
        </div>
      </div>
    </section>
    <LandingpageV2Faq class="bg-white" />
    <MarketingCTAV2 class="bg-[#F8FCFF]" />
  </main>
</template>

<script>
import PosthogMixin from '@/mixins/PosthogMixin'
export default {
  mixins: [PosthogMixin],
  data () {
    return {
      pricingTab: 'individual'
    }
  },
  computed: {
    toolNavigation () {
      return [
        { title: 'Free PFP Generator', url: '/tools/free-headshot-generator', alt: 'Free PFP Generator' },
        { title: 'Free LinkedIn Headline Generator', url: '/tools/free-linkedin-headline-generator', alt: 'Free LinkedIn Headline Generator' },
        { title: 'Free LinkedIn Bio Generator', url: '/tools/free-linkedin-bio-generator', alt: 'Free LinkedIn Bio Generator' },
        { title: 'Free LinkedIn Profile Generator', url: '/tools/free-linkedin-profile-generator', alt: 'Free LinkedIn Profile Generator' },
        { title: 'Free Email Signature Generator', url: '/tools/free-email-signature-generator', alt: 'Free Email Signature Generator' },
        { title: 'Free Team Page Generator', url: '/tools/free-team-page-generator', alt: 'Free Team Page Generator' }
      ]
    },
    headshotNavigation () {
      return this.$store.state?.navigation?.headshotTypes || []
    },
    showcaseItems () {
      return [
        {
          usecase: 'Use on your LinkedIn profile',
          quote: 'I was looking for professional work photos for my LinkedIn so this is perfect',
          name: 'Douglas Burd',
          title: 'Master of Business Administration, MBA at Universidad de la Tercera Edad',
          avatar: require('@/assets/img/landing-page/avatar-4.jpg'),
          photo: require('@/assets/img/landing-page/portrait-1.jpg')
        },
        {
          usecase: "Use on your company's website",
          quote: 'In a jam to send a prof headshot to my company and this worked amazingly.',
          name: 'Cheryl Heilman',
          title: 'President at Bankers Life Securities',
          avatar: require('@/assets/img/landing-page/avatar-5.jpg'),
          photo: require('@/assets/img/landing-page/portrait-2.jpg')
        },
        {
          usecase: 'Use on your resume/CV',
          quote: 'Easy to use + done in time as promised. Would recommend!',
          name: 'Byron Veasey',
          title: 'Data Quality Engineering Leader at Southern Company',
          avatar: require('@/assets/img/landing-page/avatar-6.jpg'),
          photo: require('@/assets/img/landing-page/portrait-3.jpg')
        }
      ]
    }
  },
  methods: {
    scrollToReviews (selector) {
      const reviewsSection = this.$el.querySelector(selector)
      if (reviewsSection) {
        reviewsSection.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }
}
</script>

<i18n>
  {
    "es": {
      "How Your Selfies Become Professional Headshots": "Convertimos tus selfies en fotos profesionales",
      "Save hundreds of dollars and hours of time by using HeadshotPro to generate your new favorite professional headshots.": "Ahorra dinero y tiempo utilizando HeadshotPro para generar tus nuevas fotos profesionales favoritas.",
      "With": "Con",
      "1. Upload your photos": "1. Sube tus fotos",
      "(5 minutes)": "(5 minutos)",
      "Use your favorite existing photos or take fresh selfies on the spot.": "Utiliza tus fotos favoritas existentes o sácate selfies en el momento.",
      "2. Let our AI work its magic": "2. Deja que nuestra IA haga su magia",
      "(1-2 hours)": "(1-2 horas)",
      "Our AI will pull your most photogenic qualities from the photos you uploaded.": "Nuestra IA extraerá tus cualidades más fotogénicas de las fotos que subiste.",
      "3. Download your favorites": "3. Descarga tus favoritas",
      "That was easy! Download your keepers and enjoy your new professional photos.": "¡Fácil! Descarga tus favoritas y disfruta de tus nuevas fotos profesionales.",
      "Physical photoshoot": "Sesión de fotos física",
      "Find a photographer you like": "Encontrar un fotógrafo que te guste",
      "Contact them and wait for a reply": "Contactarle y esperar a que responda",
      "Decide on a date and time you're both available": "Decidir una fecha y hora en la que ambos estéis disponibles",
      "Find the right clothing to wear to the photo shoot": "Encontrar la ropa adecuada para llevar a la sesión de fotos",
      "Get in your car and drive to the photo studio": "Subirte al coche y conducir al estudio de fotos",
      "Pose for your photos and pick your favorite shots": "Posar para tus fotos y elegir tus tomas favoritas",
      "Wait for the photographer to send you the photos": "Esperar a que el fotógrafo te envíe las fotos",
      "Update your professional profiles with your new photos": "Actualizar tus perfiles profesionales con tus nuevas fotos",
      "Create your AI headshots within 2 hours": "Obtén tus fotos con IA en 2 horas",
      "I updated my Linkedin profile image with this, cheaper than a studio pro with quality level better than home pro.": "Actualicé mi imagen de perfil de Linkedin con esto, más barato que un profesional de estudio con un nivel de calidad mejor que un profesional de casa.",
      "Same day results!": "¡Resultados en el mismo día!",
      "Professional headshots for 8x less than a physical photo shoot": "Fotos profesionales por 8 veces menos que una sesión de fotos física",
      "The average cost of professional headshots in the United States is": "El costo promedio de las fotos profesionales en los Estados Unidos es",
      "Our packages start from $29.": "Nuestros paquetes comienzan desde $29.",
      "We won't let you leave without at least": "No te dejaremos irte sin",
      "a handful of good headshots": "un buen puñado de fotos"
    },
    "de": {
      "How Your Selfies Become Professional Headshots": "Verwandle deine Selfies in professionelle Bewerbungsfotos",
      "Save hundreds of dollars and hours of time by using HeadshotPro to generate your new favorite professional headshots.": "Spare hunderte Euro und viele Stunden mit HeadshotPro für deine perfekten Bewerbungsfotos.",
      "With": "Mit",
      "1. Upload your photos": "1. Fotos hochladen",
      "(5 minutes)": "(5 Minuten)",
      "Use your favorite existing photos or take fresh selfies on the spot.": "Nutze deine Lieblingsfotos oder mache direkt neue Selfies.",
      "2. Let our AI work its magic": "2. Unsere KI übernimmt",
      "(1-2 hours)": "(1–2 Stunden)",
      "Our AI will pull your most photogenic qualities from the photos you uploaded.": "Unsere KI holt das Beste aus deinen Fotos heraus.",
      "3. Download your favorites": "3. Lade deine Favoriten herunter",
      "That was easy! Download your keepers and enjoy your new professional photos.": "So einfach! Lade deine besten Fotos herunter und genieße deine neuen Profi-Bilder.",
      "Physical photoshoot": "Klassisches Fotoshooting",
      "Find a photographer you like": "Einen passenden Fotografen finden",
      "Contact them and wait for a reply": "Kontakt aufnehmen und auf Antwort warten",
      "Decide on a date and time you're both available": "Einen gemeinsamen Termin vereinbaren",
      "Find the right clothing to wear to the photo shoot": "Passende Kleidung für das Shooting wählen",
      "Get in your car and drive to the photo studio": "Zum Studio fahren",
      "Pose for your photos and pick your favorite shots": "Posieren und beste Aufnahmen auswählen",
      "Wait for the photographer to send you the photos": "Auf die bearbeiteten Fotos warten",
      "Update your professional profiles with your new photos": "Profile mit den neuen Fotos aktualisieren",
      "Create your AI headshots within 2 hours": "KI-Bewerbungsfotos in nur 2 Stunden erstellen",
      "I updated my Linkedin profile image with this, cheaper than a studio pro with quality level better than home pro.": "Ich habe mein LinkedIn-Profilbild damit aktualisiert – günstiger als im Studio und besser als selbstgemacht.",
      "Same day results!": "Ergebnis noch am selben Tag!",
      "Professional headshots for 8x less than a physical photo shoot": "Professionelle Bewerbungsfotos für ein Achtel des Studio-Preises",
      "The average cost of professional headshots in the United States is": "Die durchschnittlichen Kosten für professionelle Bewerbungsfotos in den USA betragen",
      "Our packages start from $29.": "Unsere Pakete starten ab 29 €.",
      "We won't let you leave without at least": "Du bekommst garantiert mindestens",
      "a handful of good headshots": "eine Handvoll guter Bewerbungsfotos"
    }
  }
</i18n>
