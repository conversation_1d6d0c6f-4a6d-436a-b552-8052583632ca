<template>
  <section class="relative isolate overflow-hidden bg-[#f5f5f5] md:bg-white pt-4 pb-16">
    <!-- <div class="absolute bottom-0 left-0 hidden lg:left-[-267px] 2xl:left-[-200px] lg:block 3xl:left-[-170px]">
      <img class="w-full max-w-xs lg:max-w-[34rem] xl:max-w-[40rem] 2xl:max-w-[42rem]" src="@/assets/img/amelia-hero-4.png" alt="Before and after photos of a woman turned into AI headshots created by HeadshotPro" loading="lazy">
    </div>

    <div class="absolute bottom-0 right-0 mb-[-0.3rem] hidden lg:right-[-267px] 2xl:right-[-200px] lg:block 3xl:right-[-170px]">
      <img class="w-full max-w-xs lg:max-w-[32rem] xl:max-w-[38rem] 2xl:max-w-[40rem]" src="@/assets/img/hero-danny-6.png" alt="Before and after photos of a man turned into AI headshots created by HeadshotPro" loading="lazy">
    </div> -->
    <!-- Hello -->

    <div class="absolute inset-x-0 bottom-0 h-16 w-full bg-gradient-to-t from-black/20 to-black/0 z-50" />

    <div class="relative mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
      <div class="mx-auto md:max-w-3xl text-left md:text-center">
        <div class="hidden sm:mb-8 sm:flex sm:justify-center ">
          <!-- <div class="relative rounded-full px-3 py-1 text-sm leading-6 text-gray-600 ring-1 ring-gray-900/10 hover:ring-gray-900/20 bg-teal-500/10">
            <strong class="text-teal-500">{{ $t('New!') }}</strong> {{ $t('We just upgraded our') }} <button class="underline relative" @mouseover="showExample=true" @mouseleave="showExample=false">
              {{ $t("headshot quality.") }}
            </button>
          </div> -->
          <img v-if="showExample" src="@/assets/img/teams/hero-image.jpg" class="hidden md:block w-[500px] h-auto  ring-2 ring-white -mt-4 absolute top-16 right-0 shadow-xl rounded-md z-[100]" alt="Professional AI-generated business headshots for team members showcasing realistic photo quality created by HeadshotPro">
        </div>
        <div class="inline-flex flex-row w-auto md:items-center gap-3 bg-green-500/10 p-2 rounded-md items-center justify-center">
          <TrustpilotRating />
        </div>
        <ExperimentWrapper id="30-new-homepage-hero-h1-copy" type="h2" class="mt-3 text-balance text-2xl font-bold leading-tight tracking-tight text-primary-500 sm:mt-6 sm:text-4xl xl:text-[42px]">
          <template #control>
            <span v-if="$route.query.ref==='profilepictureai'">{{ $t('Professional profile pictures, without a physical photo shoot') }}</span>
            <span v-else>{{ $t('Professional business headshots, without a physical photo shoot') }}</span>
          </template>
          <template #400_photographer>
            <span>Look like you hired a $400 photographer without leaving home</span>
          </template>
          <template #realistic_ai>
            <span class="xl:text-[48px] leading-[3rem]">Realistic AI headshots that look exactly like you</span>
          </template>
          <template #skip_photoshoot>
            <span class="xl:text-[48px] leading-[3rem]">Skip the photoshoot. Keep the professional look</span>
          </template>
          <template #linkedin-ready>
            <span class="xl:text-[48px] leading-[3rem]">From selfie to LinkedIn‑ready in 2 hours</span>
          </template>
        </ExperimentWrapper>
        <h1 class="text-sm font-bold text-[#474368]/70 sm:text-md lg:text-lg mt-4">
          {{ $t('The #1 AI Headshot Generator for Professional Headshots') }}
        </h1>
        <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg">
          {{ $t('Get professional business headshots in minutes with our AI headshot generator. Upload photos, pick your styles & receive 100+ headshots.') }}
          <strong>All we need are a few selfies.</strong>
        </p>

        <div class="mt-6 flex flex-col md:flex-row gap-3 items-center justify-center">
          <client-only>
            <template v-if="!isLoggedIn">
              <nuxt-link
                :to="'/auth/login?redirect=' + encodeURIComponent('/app')"
                title=""
                class="inline-flex flex-shrink-0 h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-transparent bg-[#ff6600] px-6 pb-3.5 pt-3 text-base font-bold text-white shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
                role="button"
              >
                <span class="hidden md:inline-flex">{{ $t('Create your headshots now') }}</span>
                <span class="md:hidden">{{ $t('Create your headshots') }}</span>
                <svg class="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path fill-rule="evenodd" d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                </svg>
              </nuxt-link>
              <span class="text-xs text-[#474368]/70 uppercase hidden md:inline-flex">{{ $t('Or') }}</span>
              <nuxt-link to="/company-headshots" class="w-full md:w-auto">
                <ButtonWhite class="w-full md:w-auto">
                  <span class="hidden md:inline-flex">{{ $t('Team Packages') }}</span>
                  <span class="md:hidden">{{ $t('View Team Packages') }}</span>
                </ButtonWhite>
              </nuxt-link>
            </template>
            <template v-else>
              <nuxt-link
                to="/app"
                title=""
                class="inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-transparent bg-primary-500 px-12 pb-3.5 pt-3 text-base font-bold text-white shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
                role="button"
              >
                {{ $t('Access your photos') }}
                <svg class="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path fill-rule="evenodd" d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                </svg>
              </nuxt-link>
            </template>
          </client-only>
        </div>
      </div>
    </div>
    <div class="relative overflow-hidden px-2 mt-8 py-4">
      <div class="flex flex-row gap-2 animate-infinite-scroll">
        <!-- <div class="flex flex-row gap-2"> -->
        <!-- First set of reviews -->
        <LandingpageV3ExamplePhotoItem v-for="review in reviews.slice(0,20)" :key="`first-${review._id}`" :review="review" />
        <!-- Duplicate set for seamless loop -->
        <LandingpageV3ExamplePhotoItem v-for="review in reviews.slice(0,20)" :key="`second-${review._id}`" :review="review" />
      </div>
    </div>
    <div class="mt-4 flex flex-col items-start md:items-center justify-start md:justify-center gap-4 sm:mt-6  sm:gap-4">
      <div class="flex flex-col items-start md:items-center gap-2">
        <p class="hidden md:flex -mt-0.5 text-sm font-normal text-[#474368]/80 gap-0.5">
          <span class="hidden md:inline-flex font-bold">18M</span>
          <span class="hidden md:inline-flex"> {{ $t('headshots created for') }}</span>
          <span class="font-bold">{{ $store.state.stats.users }}+</span>
          {{ $t('happy customers') }}
        </p>

        <div class="max-w-[100%] w-[600px] mx-auto overflow-hidden relative">
          <div class="absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-[#f5f5f5] md:from-white to-transparent z-10" />
          <div class="absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-[#f5f5f5] md:from-white to-transparent z-10" />
          <div class="flex flex-row animate-scroll-slow">
            <img
              src="@/assets/img/logo-cloud-horizontal.png"
              class="w-[240%] max-w-[240%] grayscale opacity-60 flex-shrink-0"
              alt="Trusted by Fortune 500 companies including Microsoft, Amazon, Google, and leading businesses for professional AI headshots"
            >
            <img
              src="@/assets/img/logo-cloud-horizontal.png"
              class="w-[240%] max-w-[240%] grayscale opacity-60 flex-shrink-0"
              alt="Trusted by Fortune 500 companies including Microsoft, Amazon, Google, and leading businesses for professional AI headshots"
            >
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import PosthogMixin from '@/mixins/PosthogMixin'

export default {
  mixins: [PosthogMixin],
  data () {
    return {
      showExample: false
    }
  },
  computed: {
    reviews () {
      const reviews = JSON.parse(JSON.stringify(this.$store.state.reviews))

      // Prioritize reviews by criteria
      const priorityReviews = reviews.filter(review => review.trainingImage && review.quote)
      const secondaryReviews = reviews.filter(review => review.trainingImage && !review.quote)
      const remainingReviews = reviews.filter(review => !review.trainingImage)

      // Function to interleave by gender
      function interleaveByGender (reviewList) {
        const maleReviews = reviewList.filter(review => review.trigger === 'male')
        const femaleReviews = reviewList.filter(review => review.trigger === 'female')

        const mixedReviews = []
        const maxLength = Math.max(maleReviews.length, femaleReviews.length)
        for (let i = 0; i < maxLength; i++) {
          if (maleReviews[i]) { mixedReviews.push(maleReviews[i]) }
          if (femaleReviews[i]) { mixedReviews.push(femaleReviews[i]) }
        }
        return mixedReviews
      }

      // Interleave each category by gender and combine them in priority order
      const finalReviews = [
        ...interleaveByGender(priorityReviews),
        ...interleaveByGender(secondaryReviews),
        ...interleaveByGender(remainingReviews)
      ]

      return finalReviews
    }
  }

}
</script>

<style>
.animate-infinite-scroll {
  animation: scroll-left 80s linear infinite;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-188px * 20));
  }
}

/* Pause animation on hover for better UX */
.animate-infinite-scroll:hover {
  animation-play-state: paused;
}

.animate-scroll-slow {
  animation: scroll-horizontal 200s linear infinite;
}

@keyframes scroll-horizontal {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-240%);
  }
}
</style>

<i18n>
  {
    "en": {
      "New!": "New!",
      "We just upgraded our": "We just upgraded our",
      "headshot quality.": "headshot quality.",
      "The #1 AI Headshot Generator for Professional Headshots": "The #1 AI Headshot Generator for Professional Headshots",
      "Professional business headshots, without a physical photo shoot": "Professional business headshots, without a physical photo shoot",
      "Get professional business headshots in minutes with our AI headshot generator. Upload photos, pick your styles & receive 100+ headshots.": "Get professional business headshots in minutes with our AI headshot generator. Upload photos, pick your styles & receive 100+ headshots.",
      "As seen on:": "As seen on:",
      "headshots created for": "headshots created for",
      "happy customers": "happy customers",
      "Access your photos": "Access your photos",
      "Create your headshots now": "Create your headshots now",
      "Create your headshots": "Create your headshots",
      "Or": "Or",
      "Team Packages": "Team Packages",
      "View Team Packages": "View Team Packages"
    },
    "es": {
      "New!": "¡Novedad!",
      "We just upgraded our": "Acabamos de actualizar",
      "headshot quality.": "la calidad de nuestras fotos.",
      "The #1 AI Headshot Generator for Professional Headshots": "El generador de Fotos Profesionales IA #1",
      "Professional business headshots, without a physical photo shoot": "Fotos profesionales para empresas, sin una sesión física",
      "Get professional business headshots in minutes with our AI headshot generator. Upload photos, pick your styles & receive 100+ headshots.": "Consigue fotos profesionales en minutos con nuestro generador de fotos IA. Sube tus fotos, elige estilos y recibe +100 fotos.",
      "As seen on:": "Visto en:",
      "headshots created for": "fotos creadas para",
      "happy customers": "clientes satisfechos",
      "Access your photos": "Accede a tus fotos",
      "Create your headshots now": "Crea tus fotos ahora",
      "Create your headshots": "Crea tus fotos",
      "Or": "O",
      "Team Packages": "Paquetes de Equipo",
      "View Team Packages": "Ver Paquetes de Equipo"
    },
    "de": {
      "New!": "Neu!",
      "We just upgraded our": "Wir haben gerade unsere",
      "headshot quality.": "Bewerbungsfoto-Qualität verbessert.",
      "The #1 AI Headshot Generator for Professional Headshots": "Der Nr. 1 KI-Generator für professionelle Bewerbungsfotos",
      "Professional business headshots, without a physical photo shoot": "Bewerbungsfotos vom Profi, ohne Studio-Termin",
      "Get professional business headshots in minutes with our AI headshot generator. Upload photos, pick your styles & receive 100+ headshots.": "Erhalte in wenigen Minuten professionelle Bewerbungsfotos mit unserem KI-Generator. Lade Fotos hoch, wähle deine Stile und bekomme über 100 Fotos.",
      "As seen on:": "Bekannt aus:",
      "headshots created for": "Bewerbungsfotos erstellt für",
      "happy customers": "zufriedene Kunden",
      "Access your photos": "Zu deinen Fotos",
      "Create your headshots now": "Jetzt Bewerbungsfotos erstellen",
      "Create your headshots": "Bewerbungsfotos erstellen",
      "Or": "Oder",
      "Team Packages": "Team-Pakete",
      "View Team Packages": "Team-Pakete ansehen"
    }
  }
</i18n>
