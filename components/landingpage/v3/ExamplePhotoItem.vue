<template>
  <div class="flex-shrink-0 relative group rounded-md" :data-model-id="review._id">
    <div v-if="review?.trainingImage" :style="{ transform: `rotate(${randomRotation}deg)` }" class="bg-white p-0.5 rounded-md border border-black/5 shadow-xl w-[32px] h-[32px] opacity-90 group-hover:opacity-100 object-cover  top-[-8px] left-2 absolute group-hover:w-[64px] group-hover:h-[64px] transition-all duration-300">
      <img :src="review?.trainingImage" class="" :alt="`Selfie of ${review.trigger === 'male' ? 'a man' : 'a woman'} uploaded to HeadshotPro AI headshot generator`">
    </div>
    <ImageDns :src="review.image" class="w-[180px] h-[170px] object-cover rounded-md" :alt="`Professional AI headshot of ${review.trigger === 'male' ? 'a man in business attire' : 'a woman in professional attire'} created by HeadshotPro`" />

    <portal to="modal">
      <div v-if="selectedReview" class="z-50 fixed inset-0 bg-black/80 flex items-center justify-center" @click.stop="selectedReview = null">
        <div class="flex flex-col items-center justify-center relative z-10">
          <ImageDns :src="selectedReview.thumbnail" class="w-[600px] h-auto object-cover rounded-md shadow-xl" :alt="`High-resolution professional AI headshot of ${selectedReview.trigger === 'male' ? 'a man' : 'a woman'} with studio-quality lighting created by HeadshotPro`" />
          <div v-if="selectedReview?.trainingImage" class="absolute top-[-10px] left-4 w-[100px] h-[100px] rotate-[-8deg]">
            <ImageDns :src="selectedReview?.trainingImage" class="w-full h-full object-cover rounded-md shadow-xl" :alt="`Original selfie of ${selectedReview.trigger === 'male' ? 'a man' : 'a woman'} before AI transformation by HeadshotPro`" />
          </div>
          <svg
            v-if="selectedReview?.trainingImage"
            class="w-auto h-[32px] text-white/70 absolute top-6 left-[128px] rotate-[-120deg] z-20"
            viewBox="0 0 50 38"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.73613 31.9344C9.78808 33.6973 11.7607 35.4378 14.0258 37.3792C12.9909 37.9954 11.7571 37.608 10.7461 36.8492C7.71298 34.5728 4.66991 32.2147 1.75552 29.6559C0.085201 28.198 0.123864 26.6299 1.64443 26.2296C4.16052 25.5179 6.7758 24.9921 9.38111 24.3845C9.52971 24.3474 9.71296 24.2807 9.83685 24.3551C10.3225 24.5709 10.7833 24.8983 11.2789 25.1959C11.1701 25.56 11.2399 26.1324 10.9873 26.2585C10.1256 26.7261 9.20938 27.0599 8.33774 27.4458C7.86727 27.6684 7.38684 27.8093 7.05539 28.5449C7.97202 28.8428 8.93327 29.1928 9.8499 29.4907C28.7172 34.8107 43.321 26.8091 46.3967 9.63548C46.7767 7.41352 46.8744 5.07245 47.1306 2.77608C47.2242 1.97347 47.3625 1.22292 47.4561 0.420307C47.6493 0.435309 47.8525 0.532069 48.0457 0.547071C48.3432 1.1047 48.844 1.75908 48.869 2.27935C49.0435 4.02604 49.218 5.77273 49.1546 7.45236C48.5902 24.1893 36.9454 34.8016 20.0177 33.7789C16.7778 33.5759 13.3443 32.7262 10.02 32.1441C9.31644 32.0247 8.63763 31.7938 7.92417 31.5927C7.81024 31.6 7.75087 31.7412 7.73613 31.9344Z"
            />
          </svg>
        </div>
        <div class="bg-white shadow-xl p-8 rounded-md max-w-[400px] ml-[-64px] flex relative flex-col gap-4 z-20 pt-8">
          <IconSolidQuote v-if="selectedReview.review.quote" class="absolute top-6 left-8 w-4 h-4 text-gray-600" />
          <button class="absolute top-4 right-4 w-6 h-6 rounded-full bg-white hover:bg-black/10 transition-all duration-300 flex items-center justify-center" @click="selectedReview = null">
            <IconMiniXMark class="w-5 h-5 text-black" />
          </button>
          <template v-if="selectedReview.review.quote">
            <Paragraph size="sm" class="text-sm mt-4">
              {{ selectedReview.review.quote }}
            </Paragraph>
            <div class="flex flex-row gap-2 items-center justify-between">
              <Paragraph v-if="selectedReview?.review?.title || selectedReview?.review?.name" size="sm" class="text-sm font-bold !text-primary-500">
                {{ selectedReview?.review?.title || selectedReview?.review?.name }}
              </Paragraph>
              <img class="h-4 w-auto" src="@/assets/img/trustpilot-stars-5.svg" alt="5-star Trustpilot rating for HeadshotPro AI headshot service" loading="lazy">
            </div>
          </template>
          <template v-else>
            <Heading>
              Your professional headshots are just a few steps away
            </Heading>
            <ul class="flex flex-col gap-2">
              <li class="flex gap-2 items-center justify-start">
                <div class="w-5 h-5 text-sm rounded-full text-white flex items-center justify-center bg-teal-500">
                  1
                </div><Paragraph size="md" class="text-sm">
                  Upload a few selfies
                </Paragraph>
              </li>
              <li class="flex gap-2 items-center justify-start">
                <div class="w-5 h-5 text-sm rounded-full text-white flex items-center justify-center bg-teal-500">
                  2
                </div><Paragraph size="md" class="text-sm">
                  Let our AI work its magic
                </Paragraph>
              </li>
              <li class="flex gap-2 items-center justify-start">
                <div class="w-5 h-5 text-sm rounded-full text-white flex items-center justify-center bg-teal-500">
                  3
                </div><Paragraph size="md" class="text-sm">
                  Get your headshots within an hour
                </Paragraph>
              </li>
            </ul>
          </template>
          <ButtonPrimary class="!bg-[#ff6600] hover:!bg-[#ff6600]/90 hover:scale-105 transition-all duration-300 w-full" @click="startPhotoshoot">
            Get your headshots now
          </ButtonPrimary>
          <div class=" flex items-center justify-center gap-1">
            <svg aria-hidden="true" class="size-4 shrink-0 text-gray-500" viewBox="0 0 18 19" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M15.8836 7.24277C15.6164 6.96855 15.3422 6.68027 15.2367 6.43418C15.1312 6.18809 15.1383 5.82246 15.1312 5.4498C15.1242 4.76777 15.1102 3.9873 14.5687 3.4459C14.0273 2.90449 13.2469 2.89043 12.5648 2.8834C12.1922 2.87637 11.8125 2.86934 11.5805 2.77793C11.3484 2.68652 11.0461 2.39824 10.7719 2.13105C10.2867 1.66699 9.73125 1.13965 9 1.13965C8.26875 1.13965 7.71328 1.66699 7.22813 2.13105C6.95391 2.39824 6.66563 2.67246 6.41953 2.77793C6.17344 2.8834 5.80781 2.87637 5.43516 2.8834C4.75313 2.89043 3.97266 2.90449 3.43125 3.4459C2.88984 3.9873 2.87578 4.76777 2.86875 5.4498C2.86172 5.82246 2.85469 6.20215 2.76328 6.43418C2.67188 6.66621 2.38359 6.96855 2.11641 7.24277C1.65234 7.72793 1.125 8.2834 1.125 9.01465C1.125 9.7459 1.65234 10.3014 2.11641 10.7865C2.38359 11.0607 2.65781 11.349 2.76328 11.5951C2.86875 11.8412 2.86172 12.2068 2.86875 12.5795C2.87578 13.2615 2.88984 14.042 3.43125 14.5834C3.97266 15.1248 4.75313 15.1389 5.43516 15.1459C5.80781 15.1529 6.1875 15.16 6.41953 15.2514C6.65156 15.3428 6.95391 15.6311 7.22813 15.8982C7.71328 16.3623 8.26875 16.8896 9 16.8896C9.73125 16.8896 10.2867 16.3623 10.7719 15.8982C11.0461 15.6311 11.3344 15.3568 11.5805 15.2514C11.8266 15.1459 12.1922 15.1529 12.5648 15.1459C13.2469 15.1389 14.0273 15.1248 14.5687 14.5834C15.1102 14.042 15.1242 13.2615 15.1312 12.5795C15.1383 12.2068 15.1453 11.8271 15.2367 11.5951C15.3281 11.3631 15.6164 11.0607 15.8836 10.7865C16.3477 10.3014 16.875 9.7459 16.875 9.01465C16.875 8.2834 16.3477 7.72793 15.8836 7.24277ZM12.4805 7.73496L8.36016 11.6725C8.25363 11.7727 8.11265 11.8281 7.96641 11.8271C7.82232 11.8277 7.68365 11.7722 7.57969 11.6725L5.51953 9.70371C5.46239 9.65386 5.41592 9.59297 5.38291 9.52469C5.3499 9.45642 5.33104 9.38218 5.32746 9.30644C5.32387 9.23069 5.33564 9.155 5.36206 9.08392C5.38847 9.01283 5.42899 8.94783 5.48116 8.8928C5.53334 8.83778 5.59611 8.79387 5.66569 8.76372C5.73527 8.73357 5.81023 8.7178 5.88606 8.71736C5.96189 8.71692 6.03703 8.73182 6.10696 8.76116C6.17688 8.7905 6.24015 8.83367 6.29297 8.88809L7.96641 10.4842L11.707 6.91934C11.8164 6.82391 11.9584 6.77446 12.1034 6.78132C12.2484 6.78818 12.3851 6.85082 12.485 6.95614C12.5849 7.06147 12.6402 7.20132 12.6393 7.34646C12.6385 7.49161 12.5816 7.63081 12.4805 7.73496Z"
              />
            </svg>
            <span class="tracking-thight text-xs font-extrabold uppercase text-gray-500">
              100% money back guarantee
            </span>
          </div>
        </div>
      </div>
    </portal>
    <div class="opacity-0 group-hover:opacity-100 transition-all duration-300 flex absolute inset-0 bg-gradient-to-t rounded-md from-black/80 to-transparent z-10  items-end justify-end cursor-pointer" @click="openReview(review)">
      <div class="w-full h-10 flex items-center justify-center text-center">
        <span class="text-white text-sm opacity-0 group-hover:opacity-100 transition-all duration-400">Click to view</span>
      </div>
    </div>
  </div>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import Heading from '@/components/landingpage/common/H4.vue'
export default {
  components: {
    Paragraph,
    Heading
  },
  props: {
    review: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data () {
    return {
      selectedReview: null
    }
  },
  computed: {
    randomRotation () {
      return Math.floor(Math.random() * 17) - 8 // Random number between -8 and 8
    }
  },
  methods: {
    openReview (review) {
      this.selectedReview = review
    },
    startPhotoshoot () {
      this.selectedReview = null
      this.$router.push('/app/add')
    }
  }

}
</script>

<style>

</style>
