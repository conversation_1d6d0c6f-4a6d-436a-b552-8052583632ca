import Vue from 'vue'
import { detectFaces, getLargestFace, getImageSize, resizeImage, getBoundingBox, cropHead, cropBody } from '../utils/image'
import axios from '../utils/axios'

export const state = () => ({
  selectedSex: null,
  selectedEyeColor: null,
  ethnicity: null,
  selectedBodyType: null,
  selectedHeight: null,
  selectedWeight: null,
  title: '',
  selectedStyles: [],
  minimumPhotos: 15,
  minimumFullBodyPhotos: 0,
  maximumFullBodyPhotos: 3,
  selectedAge: null,
  photos: [],
  preselectedOptions: [],
  showRequirementsModal: false,
  selectedGlasses: null,
  hasFullBodyShot: false,
  modelId: null,
  isUploading: false
})

export const mutations = {
  SET_SELECTED_SEX (state, payload) {
    state.selectedSex = payload
  },
  SET_TITLE (state, payload) {
    state.title = payload
  },
  SET_ALL_SELECTED_STYLES (state, payload) {
    state.selectedStyles = payload
  },
  SET_SELECTED_STYLE (state, payload) {
    state.selectedStyles.push(payload)
  },
  CLEAR_SELECTED_STYLE (state) {
    state.selectedStyles = []
  },
  REMOVE_STYLE (state, index) {
    state.selectedStyles.splice(index, 1)
  },
  SET_PHOTOS (state, payload) {
    state.photos = payload
  },
  SET_SELECTED_EYE_COLOR (state, payload) {
    state.selectedEyeColor = payload
  },
  SET_ETHNICITY (state, payload) {
    state.ethnicity = payload
  },
  SET_SELECTED_BODY_TYPE (state, payload) {
    state.selectedBodyType = payload
  },
  SET_SELECTED_HEIGHT (state, payload) {
    state.selectedHeight = payload
  },
  SET_SELECTED_WEIGHT (state, payload) {
    state.selectedWeight = payload
  },
  SET_SELECTED_AGE (state, payload) {
    state.selectedAge = payload
  },
  SET_PRESELECTED_OPTIONS (state, payload) {
    state.preselectedOptions = payload
  },
  SET_SELECTED_GLASSES (state, payload) {
    state.selectedGlasses = payload
  },
  SET_MODEL_ID (state, payload) {
    state.modelId = payload
  },
  ADD_PENDING_PHOTO (state, payload) {
    state.photos.push({
      status: 'pending',
      ...payload
    })
  },
  ADD_UPLOADED_PHOTO (state, payload) {
    state.photos.push({
      status: 'success',
      ...payload
    })
  },
  UPDATE_PHOTO (state, { md5, payload }) {
    const index = state.photos.findIndex(p => p.md5 === md5)
    if (index === -1) {
      return
    }

    Vue.set(state.photos, index, {
      ...state.photos[index],
      ...payload
    })
  },
  SET_SHOW_REQUIREMENTS_MODAL (state, payload) {
    state.showRequirementsModal = payload
  },
  SET_HAS_FULL_BODY_SHOT (state, payload) {
    state.hasFullBodyShot = payload
  },
  SET_IS_UPLOADING (state, payload) {
    state.isUploading = payload
  }
}

export const getters = {
  selectedSex (state) {
    return state.selectedSex
  },
  selectedEyeColor (state) {
    return state.selectedEyeColor
  },
  ethnicity (state) {
    return state.ethnicity
  },
  title (state) {
    return state.title
  },
  selectedAge (state) {
    return state.selectedAge
  },
  selectedBodyType (state) {
    return state.selectedBodyType
  },
  selectedHeight (state) {
    return state.selectedHeight
  },
  selectedWeight (state) {
    return state.selectedWeight
  },
  minimumPhotos (state) {
    if (process.env.NODE_ENV === 'development') {
      return 5
    }
    return state.minimumPhotos
  },
  minimumFacePhotos (state, getters) {
    return getters.minimumPhotos - getters.minimumFullBodyPhotos
  },
  minimumFullBodyPhotos (state) {
    return state.minimumFullBodyPhotos
  },
  maximumFullBodyPhotos (state) {
    return state.maximumFullBodyPhotos
  },
  goodFullBodyPhotos (state) {
    return state.photos.filter(photo => photo.status === 'success' && photo.fullBody)
  },
  goodFacePhotos (state) {
    return state.photos.filter(photo => photo.status === 'success' && !photo.fullBody)
  },
  goodPhotos (state) {
    return state.photos.filter(photo => photo.status === 'success' || photo.status === 'pre-success')
  },
  selectedGlasses (state) {
    return state.selectedGlasses
  },
  hasEnoughTotalPhotos (state, getters) {
    return getters.goodFacePhotos.length >= getters.minimumPhotos
  }
}

export const actions = {
  fetchModelInformation ({ commit, state }, { modelId, silent = false } = {}) {
    return new Promise((resolve, reject) => {
      return axios.post('/onboarding/start', {
        modelId
      })
        .then((response) => {
          if (response.data?.data?.modelId) {
            commit('SET_MODEL_ID', response.data.data.modelId)

            if (response.data.data.model.trainingImages) {
              if (!silent) {
                commit('SET_PHOTOS', [])
              }

              for (const image of response.data.data.model.trainingImages) {
                if (silent) {
                  if (state.photos.find(p => p.md5 === image.md5)) {
                    continue
                  }

                  commit('ADD_UPLOADED_PHOTO', {
                    path: image.path,
                    url: image.url,
                    md5: image.md5,
                    file: null,
                    fullBody: image.fullBody
                  })
                } else {
                  commit('ADD_UPLOADED_PHOTO', {
                    path: image.path,
                    url: image.url,
                    md5: image.md5,
                    file: null,
                    fullBody: image.fullBody
                  })
                }
              }

              if (silent) {
                // If we are in silent mode, we didn't clean up the photos, so we need to check if there are photos locally that were not returned by the server
                // This would only happen if the user has photos in the phone and they delete them from the computer
                const photos = state.photos.filter(p => response.data.data.model.trainingImages.find(i => i.md5 === p.md5) || p.status === 'pending' || p.status === 'pre-success' || p.status === 'error')
                if (photos.length > 0) {
                  commit('SET_PHOTOS', photos)
                }
              }
            }

            if (!silent) {
              commit('SET_TITLE', response.data.data.model.title)
              commit('SET_SELECTED_AGE', response.data.data.model.age || null)
              commit('SET_ETHNICITY', response.data.data.model.ethnicity || null)
              commit('SET_SELECTED_SEX', response.data.data.model.trigger || null)
              commit('SET_SELECTED_EYE_COLOR', response.data.data.model.eyeColor || null)
              commit('SET_SELECTED_BODY_TYPE', response.data.data.model.appearance?.bodyType || null)
              commit('SET_SELECTED_HEIGHT', response.data.data.model.appearance?.height || null)
              commit('SET_SELECTED_WEIGHT', response.data.data.model.appearance?.weight || null)
              commit('SET_SELECTED_GLASSES', response.data.data.model.appearance?.glassesPreference || null)
            }

            return resolve(response.data.data.model)
          }

          return resolve(null)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  savePersonalInfo ({ commit }, payload) {
    return new Promise((resolve, reject) => {
      axios.post('/onboarding/save-personal-info', payload)
        .then((response) => {
          if (response.data?.success) {
            commit('SET_TITLE', payload.name)
            commit('SET_SELECTED_AGE', payload.age)
            commit('SET_ETHNICITY', payload.ethnicity)
            commit('SET_SELECTED_SEX', payload.gender)
            commit('SET_SELECTED_EYE_COLOR', payload.eyeColor)
            commit('SET_SELECTED_HEIGHT', payload.height)
            commit('SET_SELECTED_WEIGHT', payload.weight)
            commit('SET_SELECTED_BODY_TYPE', payload.bodyType)
            commit('SET_SELECTED_GLASSES', payload.glasses)
            return resolve()
          }

          return reject(response.data.error)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  finish ({ commit }) {
    commit('SET_PHOTOS', [])
    commit('CLEAR_SELECTED_STYLE', [])
    commit('SET_TITLE', '')
    commit('SET_SELECTED_AGE', null)
    commit('SET_ETHNICITY', null)
    commit('SET_SELECTED_SEX', null)
    commit('SET_SELECTED_EYE_COLOR', null)
    commit('SET_SELECTED_BODY_TYPE', null)
    commit('SET_SELECTED_HEIGHT', null)
    commit('SET_SELECTED_WEIGHT', null)
    commit('SET_SELECTED_GLASSES', null)
    localStorage.removeItem('onboarding.styles')
  },
  fetchPreselectedOptions ({ commit, state }) {
    if (state.preselectedOptions.length > 0) {
      return
    }

    axios.get('/model/preselected-styles')
      .then((response) => {
        if (response.data?.success) {
          commit('SET_PRESELECTED_OPTIONS', response.data.data)
        }
      })
  },
  deletePhoto ({ commit, state }, payload) {
    if (!payload) {
      return
    }

    const { md5 } = payload
    const photo = state.photos.find(p => p.md5 === md5)
    if (!photo) {
      return
    }

    commit('SET_IS_UPLOADING', true)

    const photos = state.photos.filter(p => p.md5 !== md5)
    commit('SET_PHOTOS', photos)

    if (photo.status === 'success') {
      axios.post('/onboarding/remove-image', {
        imagePath: photo.path
      }).then(() => {
        //
      }).finally(() => {
        commit('SET_IS_UPLOADING', false)
      })
    }
  },
  async addPhoto ({ commit, state }, payload) {
    let { file } = payload
    const fileName = file.name || 'trainingImage' + new Date().getTime().toString()

    let photoMd5 = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
    commit('ADD_PENDING_PHOTO', {
      file,
      md5: photoMd5
    })

    const currentUrlHasBypass = window.location.href.includes('bypass=true')

    commit('SET_IS_UPLOADING', true)

    try {
      file = await convertFromHeic(file)
      file = await resizeImageIfNeeded(file)

      const needsFullBodyPhoto = state.photos.filter(photo => (photo.status === 'success' || photo.status === 'pre-success') && photo.fullBody === true).length < state.maximumFullBodyPhotos

      const { image: croppedImage, hasFullBodyShot } = await getCroppedImage(file, fileName, currentUrlHasBypass, needsFullBodyPhoto)

      const result = await uploadFile(croppedImage, fileName, state.modelId, hasFullBodyShot)
      if (!result || !result?.md5) {
        return
      }

      const { md5 } = result
      if (state.photos.find(p => p?.md5 === md5)) {
        axios.post('/onboarding/remove-image', {
          imagePath: result.path
        }).then(() => {
          //
        })
        throw new Error('You already uploaded this photo')
      }

      commit('UPDATE_PHOTO', {
        md5: photoMd5,
        payload: {
          status: 'pre-success',
          path: result.path,
          url: result.url,
          md5: result.md5,
          file: null,
          fullBody: hasFullBodyShot
        }
      })

      photoMd5 = md5

      setTimeout(() => {
        commit('UPDATE_PHOTO', {
          md5: photoMd5,
          payload: {
            status: 'success'
          }
        })
      }, 1000)
    } catch (err) {
      commit('UPDATE_PHOTO', {
        md5: photoMd5,
        payload: {
          status: 'error',
          errorMessage: err.message,
          md5: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15) // to allow reupload
        }
      })
      throw err
    } finally {
      commit('SET_IS_UPLOADING', false)
    }
  }
}

async function convertFromHeic (file) {
  if (file.type === 'image/heic' || file.type === 'image/heif') {
    const heic2any = (await import('heic2any')).default
    return await heic2any({ blob: file, toType: 'image/png' })
  }

  return file
}

async function resizeImageIfNeeded (file) {
  const size = await getImageSize(file)
  if (size.width < 512 || size.height < 512) {
    throw new Error('Image is too small')
  } else if (size.width > 1024 || size.height > 1024) {
    return await resizeImage(file, 1024)
  }

  return file
}

async function getCroppedImage (fileData, fileName, shouldBypassDetection = false, needsFullBodyPhoto = false) {
  const blob = new File([fileData], fileName, { type: fileData.type })
  const data = await detectFaces(blob, shouldBypassDetection)
  const { boundingBoxes: faces, faceCoveragePercentage } = data?.data || {}

  if (!faces || faces?.length === 0) {
    throw new Error(faces?.errorMessage || 'We could not find any faces in the photo.')
  }

  const largestFace = getLargestFace(faces)
  const boundingBox = getBoundingBox(largestFace)

  if (faceCoveragePercentage < 20 && needsFullBodyPhoto) {
    const { image } = await cropBody(fileData, boundingBox)
    return { image, hasFullBodyShot: true }
  }

  const { image: croppedImage, success, message } = await cropHead(fileData, boundingBox)
  if (success) {
    return { image: croppedImage, hasFullBodyShot: false }
  }

  throw new Error(message)
}

async function uploadFile (image, fileName, modelId, isFullBody = false) {
  try {
    // Base64 to file
    const dataURItoBlob = (dataURI) => {
      const byteString = window.atob(dataURI.split(',')[1])
      const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0]
      const ab = new ArrayBuffer(byteString.length)
      const ia = new Uint8Array(ab)
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i)
      }
      return new Blob([ab], { type: mimeString })
    }

    const imageBlob = dataURItoBlob(image)
    const newFile = new File([imageBlob], fileName, {
      type: imageBlob.type
    })

    // Formdata
    const formData = new FormData()
    formData.append('files', newFile)
    formData.append('requiresFullUrl', '1')
    formData.append('computeMd5', '1')
    formData.append('modelId', modelId)
    formData.append('isFullBody', isFullBody)

    console.log('isFullBody:', isFullBody)

    const response = (await axios.post('/image/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }))?.data

    if (response && response.success) {
      return response.data
    }

    throw new Error('Could not approve this photo.')
  } catch (err) {
    if (err?.type === 'error') {
      throw new Error('Cannot upload this photo. Please try another photo.')
    }

    if (err.message === 'Canceled') {
      return
    }

    throw new Error(err.message || err || 'Something went wrong. Please try another photo.')
  }
}
